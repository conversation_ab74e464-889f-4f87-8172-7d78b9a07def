import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const RISK: AppRouteRecordRaw = {
  path: '/risk',
  name: 'risk',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.risk',
    requiresAuth: true,
    icon: 'icon-shield',
    order: 4,
  },
  children: [
    {
      path: 'reports',
      name: 'RiskReports',
      component: () => import('@/views/risk/reports/index.vue'),
      meta: {
        locale: 'menu.risk.reports',
        requiresAuth: true,
        roles: ['*'],
      },
    },
  ],
};

export default RISK;