package statistics

import (
	"context"
	"fmt"
	"sync"
	"time"

	"fincore/utils/convert"
	"fincore/utils/gform"
	"fincore/utils/log"
	"fincore/utils/pagination"
	"fincore/utils/shopspringutils"

	"github.com/gogf/gf/v2/util/gconv"
	"github.com/golang-module/carbon/v2"
	"golang.org/x/sync/errgroup"
)

// Service 渠道统计服务层
type Service struct {
	ctx        context.Context
	repository *Repository
	logger     *log.Logger
}

// NewService 创建渠道统计服务实例
func NewService(ctx context.Context) *Service {
	return &Service{
		ctx:        ctx,
		repository: NewRepository(ctx),
		logger:     log.RegisterModule("statistics", "统计服务"),
	}
}

// ExecuteChannelStatistics 执行渠道统计
// 这是主要的统计执行方法，会统计所有启用渠道的当天数据
func (s *Service) ExecuteChannelStatistics() error {
	s.logger.Info("开始执行渠道统计任务")

	// 获取今天的时间范围
	startTime, endTime := GetTodayTimeRange()
	s.logger.Info("统计时间范围",
		log.String("start_time", FormatTimeForLog(startTime)),
		log.String("end_time", FormatTimeForLog(endTime)),
	)

	// 获取所有启用的渠道
	channels, err := s.repository.GetEnabledChannels()
	if err != nil {
		s.logger.Error("获取启用渠道列表失败", log.String("error", err.Error()))
		return fmt.Errorf("获取启用渠道列表失败: %v", err)
	}

	if len(channels) == 0 {
		s.logger.Warn("没有找到启用的渠道")
		return nil
	}

	s.logger.Info("找到启用渠道", log.Int("channel_count", len(channels)))

	// 使用并发处理多个渠道的统计
	var wg sync.WaitGroup
	errChan := make(chan error, len(channels))

	for _, channelData := range channels {
		wg.Add(1)
		go func(data gform.Data) {
			defer wg.Done()

			channelInfo, err := ConvertChannelData(data)
			if err != nil {
				errChan <- fmt.Errorf("转换渠道数据失败: %v", err)
				return
			}

			err = s.statisticsForChannel(channelInfo.ID, channelInfo.ChannelName, startTime, endTime)
			if err != nil {
				errChan <- fmt.Errorf("渠道[%s]统计失败: %v", channelInfo.ChannelName, err)
				return
			}
		}(channelData)
	}

	// 等待所有goroutine完成
	wg.Wait()
	close(errChan)

	// 检查是否有错误
	var errors []error
	for err := range errChan {
		errors = append(errors, err)
		s.logger.Error("渠道统计出现错误", log.String("error", err.Error()))
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分渠道统计失败，错误数量: %d", len(errors))
	}

	s.logger.Info("渠道统计任务执行完成",
		log.Int("success_channel_count", len(channels)),
	)

	return nil
}

// statisticsForChannel 为单个渠道执行统计
func (s *Service) statisticsForChannel(channelID uint, channelName string, startTime, endTime time.Time) error {
	s.logger.Info("开始统计渠道数据",
		log.Uint("channel_id", channelID),
		log.String("channel_name", channelName),
	)

	// 统计新用户数
	newUserCount, err := s.repository.GetNewUserCountByChannel(channelID, startTime, endTime)
	if err != nil {
		return fmt.Errorf("统计新用户数失败: %v", err)
	}

	// 统计实名通过数
	realNameCount, err := s.repository.GetRealNameCountByChannel(channelID, startTime, endTime)
	if err != nil {
		return fmt.Errorf("统计实名通过数失败: %v", err)
	}

	// 统计成交数
	transactionCount, err := s.repository.GetTransactionCountByChannel(channelID, startTime, endTime)
	if err != nil {
		return fmt.Errorf("统计成交数失败: %v", err)
	}

	s.logger.Info("渠道统计数据",
		log.Uint("channel_id", channelID),
		log.String("channel_name", channelName),
		log.Uint("new_user_count", newUserCount),
		log.Uint("real_name_count", realNameCount),
		log.Uint("transaction_count", transactionCount),
	)

	// 保存统计数据
	date := carbon.CreateFromStdTime(startTime).StartOfDay()
	err = s.repository.SaveChannelStatistics(channelID, newUserCount, realNameCount, transactionCount, *date)
	if err != nil {
		return fmt.Errorf("保存统计数据失败: %v", err)
	}

	s.logger.Info("渠道统计完成",
		log.Uint("channel_id", channelID),
		log.String("channel_name", channelName),
	)

	return nil
}

// ExecuteChannelStatisticsForDate 执行指定日期的渠道统计
// 用于补充统计或重新统计某一天的数据
func (s *Service) ExecuteChannelStatisticsForDate(date carbon.Carbon) error {
	s.logger.Info("开始执行指定日期的渠道统计",
		log.String("date", date.Format("Y-m-d")),
	)

	startTime, endTime := GetSpecificDateTimeRange(date.StdTime())

	// 获取所有启用的渠道
	channels, err := s.repository.GetEnabledChannels()
	if err != nil {
		return fmt.Errorf("获取启用渠道列表失败: %v", err)
	}

	for _, channelData := range channels {
		channelInfo, err := ConvertChannelData(channelData)
		if err != nil {
			s.logger.Error("转换渠道数据失败", log.String("error", err.Error()))
			continue
		}

		err = s.statisticsForChannel(channelInfo.ID, channelInfo.ChannelName, startTime, endTime)
		if err != nil {
			s.logger.Error("渠道统计失败",
				log.String("channel_name", channelInfo.ChannelName),
				log.String("error", err.Error()),
			)
			continue
		}
	}

	s.logger.Info("指定日期的渠道统计任务执行完成",
		log.String("date", date.Format("Y-m-d")),
	)

	return nil
}

// GetStatisticsSummary 获取统计摘要信息
func (s *Service) GetStatisticsSummary() (map[string]interface{}, error) {
	channels, err := s.repository.GetEnabledChannels()
	if err != nil {
		return nil, err
	}

	summary := map[string]interface{}{
		"total_channels":    len(channels),
		"execution_time":    carbon.Now().Format("Y-m-d H:i:s"),
		"time_range":        GetTimeRangeDescription(GetTodayTimeRange()),
		"is_execution_time": IsExecutionTime(),
	}

	return summary, nil
}

// GetChannelStatisticsList 获取渠道统计列表
func (s *Service) GetChannelStatisticsList(params map[string]interface{}) (*pagination.PaginationResponse, error) {
	s.logger.Info("开始获取渠道统计列表", log.Any("params", params))

	// 构建分页请求参数
	page := convert.GetIntFromMap(params, "page", 1)
	pageSize := convert.GetIntFromMap(params, "page_size", 10)

	paginationReq := pagination.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}
	paginationReq.ValidateAndNormalize()

	// 调用repository层获取数据
	result, err := s.repository.GetChannelStatisticsListByParams(params, paginationReq)
	if err != nil {
		s.logger.Error("获取渠道统计列表失败", log.String("error", err.Error()))
		return nil, fmt.Errorf("获取渠道统计列表失败: %v", err)
	}

	// 格式化时间字段
	s.formatTimeFields(result)

	s.logger.Info("获取渠道统计列表成功",
		log.Int64("total", result.Total),
		log.Int("page", result.Page),
		log.Int("pageSize", result.PageSize),
	)

	return result, nil
}

// formatTimeFields 格式化时间字段
func (s *Service) formatTimeFields(result *pagination.PaginationResponse) {
	if result == nil || result.Data == nil {
		return
	}

	if dataSlice, ok := result.Data.([]gform.Data); ok {
		for i, item := range dataSlice {
			if createdAt, exists := item["created_at"]; exists {
				if timeStr, ok := createdAt.(string); ok {
					// 解析时间字符串
					if parsedTime, err := time.Parse(time.RFC3339, timeStr); err == nil {
						// 格式化为 yyyy-mm-dd HH:ii:ss 格式
						item["created_at"] = parsedTime.Format("2006-01-02 15:04:05")
					}
				} else if timeVal, ok := createdAt.(time.Time); ok {
					// 如果是time.Time类型，直接格式化
					item["created_at"] = timeVal.Format("2006-01-02 15:04:05")
				}
			}
			if updatedAt, exists := item["updated_at"]; exists {
				if timeStr, ok := updatedAt.(string); ok {
					if parsedTime, err := time.Parse(time.RFC3339, timeStr); err == nil {
						item["updated_at"] = parsedTime.Format("2006-01-02 15:04:05")
					}
				} else if timeVal, ok := updatedAt.(time.Time); ok {
					item["updated_at"] = timeVal.Format("2006-01-02 15:04:05")
				}
			}
			dataSlice[i] = item
		}
		result.Data = dataSlice
	}
}

type GetHomeStatisticsResponse struct {
	DisbursementAmount          float64 `json:"disbursement_amount"`           // 放款金额（本金+利息+担保） - 前置金额
	DisbursementPrincipalAmount float64 `json:"disbursement_principal_amount"` // 放款本金
	DisbursementCustomerCount   int     `json:"disbursement_customer_count"`   // 放款客户数
	DisbursementOrderCount      int     `json:"disbursement_order_count"`      // 放款订单
	DueAmount                   float64 `json:"due_amount"`                    // 到期金额（本金+利息+担保） - 前置金额
	DueRepaymentAmount          float64 `json:"due_repayment_amount"`          // 到期回款总额（本息 + 担保）
	DueRepaymentRate            float64 `json:"due_repayment_rate"`            // 到期回款率 （回款总额/到期金额）
	DueFundsRecoveryRate        float64 `json:"due_funds_recovery_rate"`       // 到期资金回收率（回款总额/本金）
	OverdueCustomerCount        int     `json:"overdue_customer_count"`        // 逾期客户
	OverdueAmount               float64 `json:"overdue_amount"`                // 逾期总额（本息+担保）
}

// GetTrendStatisticsResponse 趋势统计响应结构
type GetTrendStatisticsResponse struct {
	TrendData []TrendDataPoint `json:"trend_data"` // 趋势数据列表
}

// GetHomeStatistics 首页数据统计
// 默认统计所有数据，支持统计指定日期
func (s *Service) GetHomeStatistics(dateBegin, dateEnd time.Time) (resp GetHomeStatisticsResponse, err error) {
	startTime := time.Now()

	// 使用errgroup并行获取统计数据
	var disbursementStats *DisbursementStatistics
	var dueStats *DueStatistics
	var overdueStats *OverdueStatistics

	g, _ := errgroup.WithContext(s.ctx)

	// 并行获取放款统计数据
	g.Go(func() error {
		disbursementStats, err = s.repository.GetDisbursementStatistics(dateBegin, dateEnd)
		if err != nil {
			s.logger.WithFields(
				log.String("error", err.Error()),
			).Error("获取放款统计数据失败")
			return fmt.Errorf("获取放款统计数据失败: %v", err)
		}
		return nil
	})

	// 并行获取到期统计数据
	g.Go(func() error {
		dueStats, err = s.repository.GetDueStatistics(dateBegin, dateEnd)
		if err != nil {
			s.logger.WithFields(
				log.String("error", err.Error()),
			).Error("获取到期统计数据失败")
			return fmt.Errorf("获取到期统计数据失败: %v", err)
		}
		return nil
	})

	// 并行获取逾期统计数据
	g.Go(func() error {
		overdueStats, err = s.repository.GetOverdueStatistics()
		if err != nil {
			s.logger.WithFields(
				log.String("error", err.Error()),
			).Error("获取逾期统计数据失败")
			return fmt.Errorf("获取逾期统计数据失败: %v", err)
		}
		return nil
	})

	if err := g.Wait(); err != nil {
		return resp, err
	}

	// 计算到期回款率：到期回款总额 / 到期金额 * 100
	var dueRepaymentRate float64
	if shopspringutils.CompareAmountsWithDecimal(dueStats.DueAmount, 0) > 0 {
		rate := shopspringutils.DivideAmountsWithDecimal(dueStats.DueRepaymentAmount, dueStats.DueAmount)
		dueRepaymentRate = shopspringutils.MultiplyAmountsWithDecimal(rate, 100)
	}

	// 计算到期资金回收率：到期回款总额 / 到期本金总额 * 100
	var dueFundsRecoveryRate float64
	if shopspringutils.CompareAmountsWithDecimal(dueStats.DuePrincipalAmount, 0) > 0 {
		rate := shopspringutils.DivideAmountsWithDecimal(dueStats.DueRepaymentAmount, dueStats.DuePrincipalAmount)
		dueFundsRecoveryRate = shopspringutils.MultiplyAmountsWithDecimal(rate, 100)
	}

	// 组装返回结果
	resp = GetHomeStatisticsResponse{
		DisbursementAmount:          disbursementStats.DisbursementAmount,
		DisbursementPrincipalAmount: disbursementStats.DisbursementPrincipalAmount,
		DisbursementCustomerCount:   disbursementStats.DisbursementCustomerCount,
		DisbursementOrderCount:      disbursementStats.DisbursementOrderCount,
		DueAmount:                   dueStats.DueAmount,
		DueRepaymentAmount:          dueStats.DueRepaymentAmount,
		DueRepaymentRate:            shopspringutils.CeilToTwoDecimal(dueRepaymentRate),
		DueFundsRecoveryRate:        shopspringutils.CeilToTwoDecimal(dueFundsRecoveryRate),
		OverdueCustomerCount:        overdueStats.OverdueCustomerCount,
		OverdueAmount:               overdueStats.OverdueAmount,
	}

	// 记录执行时间和结果
	duration := time.Since(startTime)
	s.logger.WithFields(
		log.Float64("disbursement_amount", resp.DisbursementAmount),
		log.Int("disbursement_customer_count", resp.DisbursementCustomerCount),
		log.Int("disbursement_order_count", resp.DisbursementOrderCount),
		log.Float64("due_amount", resp.DueAmount),
		log.Float64("due_repayment_amount", resp.DueRepaymentAmount),
		log.Float64("due_repayment_rate", resp.DueRepaymentRate),
		log.Float64("due_funds_recovery_rate", resp.DueFundsRecoveryRate),
		log.Int("overdue_customer_count", resp.OverdueCustomerCount),
		log.Float64("overdue_amount", resp.OverdueAmount),
		log.String("duration", duration.String()),
	).Info("首页统计数据获取完成")

	return resp, nil
}

// GetTrendStatistics 获取趋势统计数据
func (s *Service) GetTrendStatistics(days int) (GetTrendStatisticsResponse, error) {
	// 记录开始时间
	startTime := time.Now()
	s.logger.WithFields(
		log.Int("days", days),
	).Info("开始获取趋势统计数据")

	// 使用errgroup并行获取4种趋势数据
	var disbursementTrend []TrendDataPoint
	var repaymentTrend []TrendDataPoint
	var dueAmountTrend []TrendDataPoint
	var registrationTrend []TrendDataPoint

	g, _ := errgroup.WithContext(s.ctx)

	// 并行获取放款金额趋势数据
	g.Go(func() error {
		var err error
		disbursementTrend, err = s.repository.GetDisbursementTrendData(days)
		if err != nil {
			s.logger.WithFields(
				log.Int("days", days),
				log.String("error", err.Error()),
			).Error("获取放款金额趋势数据失败")
			return fmt.Errorf("获取放款金额趋势数据失败: %v", err)
		}
		return nil
	})

	// 并行获取回款金额趋势数据
	g.Go(func() error {
		var err error
		repaymentTrend, err = s.repository.GetRepaymentTrendData(days)
		if err != nil {
			s.logger.WithFields(
				log.Int("days", days),
				log.String("error", err.Error()),
			).Error("获取回款金额趋势数据失败")
			return fmt.Errorf("获取回款金额趋势数据失败: %v", err)
		}
		return nil
	})

	// 并行获取到期金额趋势数据
	g.Go(func() error {
		var err error
		dueAmountTrend, err = s.repository.GetDueAmountTrendData(days)
		if err != nil {
			s.logger.WithFields(
				log.Int("days", days),
				log.String("error", err.Error()),
			).Error("获取到期金额趋势数据失败")
			return fmt.Errorf("获取到期金额趋势数据失败: %v", err)
		}
		return nil
	})

	// 并行获取注册量趋势数据
	g.Go(func() error {
		var err error
		registrationTrend, err = s.repository.GetRegistrationTrendData(days)
		if err != nil {
			s.logger.WithFields(
				log.Int("days", days),
				log.String("error", err.Error()),
			).Error("获取注册量趋势数据失败")
			return fmt.Errorf("获取注册量趋势数据失败: %v", err)
		}
		return nil
	})

	// 等待所有goroutine完成
	if err := g.Wait(); err != nil {
		return GetTrendStatisticsResponse{}, err
	}

	// 合并趋势数据
	trendDataMap := make(map[string]*TrendDataPoint)

	// 生成完整的日期列表（SQL查询的是从days天前到昨天的数据）
	// 例如：days=7时，查询从7天前到昨天，共7天的数据
	for i := days; i >= 1; i-- {
		date := carbon.Now().SubDays(i).Format("Y-m-d")
		trendDataMap[date] = &TrendDataPoint{
			Date:               date,
			DisbursementAmount: 0,
			RepaymentAmount:    0,
			DueAmount:          0,
			RepaymentRate:      0,
			RegistrationCount:  0,
		}
	}

	// 填充放款金额数据
	for _, data := range disbursementTrend {
		if point, exists := trendDataMap[data.Date]; exists {
			point.DisbursementAmount = data.DisbursementAmount
		}
	}

	// 填充回款金额数据
	for _, data := range repaymentTrend {
		if point, exists := trendDataMap[data.Date]; exists {
			point.RepaymentAmount = data.RepaymentAmount
		}
	}

	// 填充到期金额数据
	for _, data := range dueAmountTrend {
		if point, exists := trendDataMap[data.Date]; exists {
			point.DueAmount = data.DueAmount
		}
	}

	// 填充注册量数据
	for _, data := range registrationTrend {
		if point, exists := trendDataMap[data.Date]; exists {
			point.RegistrationCount = data.RegistrationCount
		}
	}

	// 计算回款率：回款金额 / 到期金额 * 100
	for _, point := range trendDataMap {
		if shopspringutils.CompareAmountsWithDecimal(point.DueAmount, 0) > 0 {
			// 使用shopspringutils计算回款率：回款金额 / 到期金额 * 100
			rate := shopspringutils.DivideAmountsWithDecimal(point.RepaymentAmount, point.DueAmount)
			point.RepaymentRate = shopspringutils.CeilToTwoDecimal(shopspringutils.MultiplyAmountsWithDecimal(rate, 100))
		} else {
			point.RepaymentRate = 0
		}
	}

	// 转换为有序列表（按时间顺序从早到晚）
	var trendData []TrendDataPoint
	for i := days; i >= 1; i-- {
		date := carbon.Now().SubDays(i).Format("Y-m-d")
		if point, exists := trendDataMap[date]; exists {
			trendData = append(trendData, *point)
		}
	}

	// 记录执行时间
	duration := time.Since(startTime)
	s.logger.WithFields(
		log.Int("days", days),
		log.Int("data_points", len(trendData)),
		log.String("duration", duration.String()),
	).Info("趋势统计数据获取完成")

	return GetTrendStatisticsResponse{
		TrendData: trendData,
	}, nil
}

// IncomeDetailsResponse 收入明细响应结构
type IncomeDetailsResponse struct {
	Statistics IncomeStatistics               `json:"statistics"` // 统计数据
	List       *pagination.PaginationResponse `json:"list"`       // 列表数据
}

// IncomeStatistics 收入统计结构
type IncomeStatistics struct {
	TotalIncome    float64 `json:"total_income"`    // 收款合计
	TotalRefund    float64 `json:"total_refund"`    // 退款合计
	DueIncome      float64 `json:"due_income"`      // 到期收款合计
	OverdueIncome  float64 `json:"overdue_income"`  // 逾期收款合计
	EarlyIncome    float64 `json:"early_income"`    // 提前处理收款合计
	TotalCustomers int     `json:"total_customers"` // 收款人数合计
	TotalOrders    int     `json:"total_orders"`    // 收款订单数合计
}

// GetIncomeDetailsList 获取收入明细列表
func (s *Service) GetIncomeDetailsList(params map[string]interface{}) (resp *IncomeDetailsResponse, err error) {
	s.logger.Info("开始获取收入明细列表", log.Any("params", params))

	// 构建分页请求参数
	page := convert.GetIntFromMap(params, "page", 1)
	pageSize := convert.GetIntFromMap(params, "page_size", 10)

	paginationReq := pagination.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}
	paginationReq.ValidateAndNormalize()

	// 并行获取统计数据和列表数据
	var statistics *IncomeStatistics
	var listData *pagination.PaginationResponse

	g, _ := errgroup.WithContext(s.ctx)

	// 并行获取统计数据
	g.Go(func() error {
		statistics, err = s.repository.GetIncomeDetailsStatistics(params)
		if err != nil {
			s.logger.Error("获取收入统计数据失败", log.String("error", err.Error()))
			return fmt.Errorf("获取收入统计数据失败: %v", err)
		}
		return nil
	})

	// 并行获取列表数据
	g.Go(func() error {
		listData, err = s.repository.GetIncomeDetailsList(params, paginationReq)
		if err != nil {
			s.logger.Error("获取收入明细列表失败", log.String("error", err.Error()))
			return fmt.Errorf("获取收入明细列表失败: %v", err)
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return
	}

	// 处理列表数据
	s.processIncomeDetailsList(listData)

	resp = &IncomeDetailsResponse{
		Statistics: *statistics,
		List:       listData,
	}

	s.logger.Info("获取收入明细列表成功",
		log.Int64("total", listData.Total),
		log.Int("page", listData.Page),
		log.Int("pageSize", listData.PageSize),
	)

	return
}

// processIncomeDetailsList 处理收入明细列表数据
func (s *Service) processIncomeDetailsList(result *pagination.PaginationResponse) {
	if result == nil || result.Data == nil {
		return
	}

	if dataSlice, ok := result.Data.([]gform.Data); ok {
		for i, item := range dataSlice {
			// 格式化收款方式
			item["payment_method_text"] = DeterminePaymentMethod(item)

			// 计算收款状态
			// item["payment_status_text"] = DeterminePaymentStatus(item)

			// 格式化款项类型
			if paymentType, exists := item["type"]; exists {
				item["fund_type_text"] = DetermineFundType(gconv.String(paymentType))
			}

			// 格式化时间
			if completedAt, exists := item["completed_at"]; exists {
				if timeVal, ok := completedAt.(time.Time); ok {
					item["completed_at"] = timeVal.Format("2006-01-02 15:04:05")
				}
			}

			if dueDate, exists := item["due_date"]; exists {
				if timeVal, ok := dueDate.(time.Time); ok {
					item["due_date"] = timeVal.Format("2006-01-02")
				}
			}

			dataSlice[i] = item
		}
		result.Data = dataSlice
	}
}

// ChannelDueStatisticsResponse 渠道到期统计响应结构
type ChannelDueStatisticsResponse struct {
	Statistics ChannelDueStatisticsSummary    `json:"statistics"`
	Pagination *pagination.PaginationResponse `json:"pagination"`
}

// ChannelDueStatisticsSummary 渠道到期统计汇总
type ChannelDueStatisticsSummary struct {
	TotalDueBills        int    `json:"total_due_bills"`         // 到期账单数
	NewUserBills         int    `json:"new_user_bills"`          // 新用户账单数
	OldUserBills         int    `json:"old_user_bills"`          // 老用户账单数
	TotalDueAmount       string `json:"total_due_amount"`        // 到期金额
	NewUserDueAmount     string `json:"new_user_due_amount"`     // 新用户到期金额
	OldUserDueAmount     string `json:"old_user_due_amount"`     // 老用户到期金额
	TotalRepaymentRate   string `json:"total_repayment_rate"`    // 金额回款率
	NewUserRepaymentRate string `json:"new_user_repayment_rate"` // 新用户金额回款率
	OldUserRepaymentRate string `json:"old_user_repayment_rate"` // 老用户金额回款率
}

// ChannelDueStatisticsItem 渠道到期统计列表项
type ChannelDueStatisticsItem struct {
	ChannelName          string `json:"channel_name"`            // 渠道名称
	DueBillsCount        int    `json:"due_bills_count"`         // 到期账单数
	NewUserBills         int    `json:"new_user_bills"`          // 新用户账单数
	OldUserBills         int    `json:"old_user_bills"`          // 老用户账单数
	DueAmount            string `json:"due_amount"`              // 到期金额
	NewUserDueAmount     string `json:"new_user_due_amount"`     // 新用户到期金额
	OldUserDueAmount     string `json:"old_user_due_amount"`     // 老用户到期金额
	AvgBillAmount        string `json:"avg_bill_amount"`         // 账单件均
	RepaymentRate        string `json:"repayment_rate"`          // 金额回款率
	NewUserRepaymentRate string `json:"new_user_repayment_rate"` // 新用户金额回款率
	OldUserRepaymentRate string `json:"old_user_repayment_rate"` // 老用户金额回款率
	RepaymentUsers       int    `json:"repayment_users"`         // 还款人数（包括主动还款、系统代扣和人工代扣场景）
	RepeatPurchaseUsers  int    `json:"repeat_purchase_users"`   // 还款复购人数（还款且为复购的用户数）
	RepeatPurchaseRate   string `json:"repeat_purchase_rate"`    // 复购率（复购人数/总下单人数×100%）
}

// GetChannelDueStatistics 获取渠道到期统计
func (s *Service) GetChannelDueStatistics(params map[string]interface{}) (resp *ChannelDueStatisticsResponse, err error) {
	startTime := time.Now()
	s.logger.Info("开始获取渠道到期统计数据")

	resp = &ChannelDueStatisticsResponse{
		Statistics: ChannelDueStatisticsSummary{},
		Pagination: &pagination.PaginationResponse{},
	}

	// 构建分页请求参数
	page := convert.GetIntFromMap(params, "page", 1)
	pageSize := convert.GetIntFromMap(params, "page_size", 10)

	paginationReq := pagination.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}
	paginationReq.ValidateAndNormalize()

	// 使用errgroup并行执行查询
	g, _ := errgroup.WithContext(s.ctx)

	var (
		basicStats          []BasicBillStats
		userTypeStats       []UserTypeStats
		repaymentStats      []RepaymentUserStats
		repeatPurchaseStats []RepeatPurchaseStats
		listData            *pagination.PaginationResponse
	)

	// 基础统计
	g.Go(func() error {
		basicStats, err = s.repository.GetBasicBillStatistics(params)
		if err != nil {
			return fmt.Errorf("获取基础账单统计失败: %v", err)
		}
		return nil
	})

	// 新老用户统计
	g.Go(func() error {
		userTypeStats, err = s.repository.GetUserTypeStatistics(params)
		if err != nil {
			return fmt.Errorf("获取新老用户统计失败: %v", err)
		}
		return nil
	})

	// 还款人数统计
	g.Go(func() error {
		repaymentStats, err = s.repository.GetRepaymentUserStatistics(params)
		if err != nil {
			return fmt.Errorf("获取还款人数统计失败: %v", err)
		}
		return nil
	})

	// 复购统计
	g.Go(func() error {
		repeatPurchaseStats, err = s.repository.GetRepeatPurchaseStatistics(params)
		if err != nil {
			return fmt.Errorf("获取复购统计失败: %v", err)
		}
		return nil
	})

	// 分页列表
	g.Go(func() error {
		listData, err = s.repository.GetChannelDueStatisticsList(params, paginationReq)
		if err != nil {
			return fmt.Errorf("获取渠道列表失败: %v", err)
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return
	}

	// 组合结果
	resp = s.combineChannelDueStatisticsResults(basicStats, userTypeStats, repaymentStats, repeatPurchaseStats, listData)

	// 计算列表数量用于日志
	listCount := 0
	if resp.Pagination != nil && resp.Pagination.Data != nil {
		if list, ok := resp.Pagination.Data.([]ChannelDueStatisticsItem); ok {
			listCount = len(list)
		}
	}

	s.logger.WithFields(
		log.String("duration", time.Since(startTime).String()),
		log.Int("channels_count", listCount),
	).Info("渠道到期统计数据获取完成")

	return
}

// combineChannelDueStatisticsResults 组合渠道到期统计结果
func (s *Service) combineChannelDueStatisticsResults(
	basicStats []BasicBillStats,
	userTypeStats []UserTypeStats,
	repaymentStats []RepaymentUserStats,
	repeatPurchaseStats []RepeatPurchaseStats,
	listData *pagination.PaginationResponse,
) *ChannelDueStatisticsResponse {
	// 创建渠道数据映射
	channelMap := make(map[int]*ChannelDueStatisticsItem)

	// 处理基础统计数据
	for _, stat := range basicStats {
		channelMap[stat.ChannelID] = &ChannelDueStatisticsItem{
			ChannelName:   stat.ChannelName,
			DueBillsCount: stat.TotalBills,
			DueAmount:     FormatAmount(stat.TotalDueAmount),
			RepaymentRate: CalculateRepaymentRate(stat.TotalPaidAmount, stat.TotalDueAmount),
			AvgBillAmount: CalculateAvgBillAmount(stat.TotalDueAmount, stat.TotalBills),
		}
	}

	// 处理新老用户统计数据
	for _, stat := range userTypeStats {
		if item, exists := channelMap[stat.ChannelID]; exists {
			item.NewUserBills = stat.NewUserBills
			item.OldUserBills = stat.OldUserBills
			item.NewUserDueAmount = FormatAmount(stat.NewUserDueAmount)
			item.OldUserDueAmount = FormatAmount(stat.OldUserDueAmount)
			item.NewUserRepaymentRate = CalculateRepaymentRate(stat.NewUserPaidAmount, stat.NewUserDueAmount)
			item.OldUserRepaymentRate = CalculateRepaymentRate(stat.OldUserPaidAmount, stat.OldUserDueAmount)
		}
	}

	// 处理还款人数统计数据
	for _, stat := range repaymentStats {
		if item, exists := channelMap[stat.ChannelID]; exists {
			item.RepaymentUsers = stat.RepaymentUsers
		}
	}

	// 处理复购统计数据
	for _, stat := range repeatPurchaseStats {
		if item, exists := channelMap[stat.ChannelID]; exists {
			item.RepeatPurchaseUsers = stat.RepeatPurchaseUsers
			// 复购率 = 复购用户数 / 下单用户数 * 100%
			item.RepeatPurchaseRate = CalculateRepeatPurchaseRate(stat.TotalRepurchaseUsers, stat.TotalOrderUsers)
		}
	}

	// 构建列表数据并替换分页结果中的data
	var list []ChannelDueStatisticsItem
	if listData != nil && listData.Data != nil {
		if dataSlice, ok := listData.Data.([]gform.Data); ok {
			for _, data := range dataSlice {
				channelID := gconv.Int(data["channel_id"])
				if item, exists := channelMap[channelID]; exists {
					list = append(list, *item)
				}
			}
		}
		// 将完整的统计列表数据替换分页结果中的基础数据
		listData.Data = list
	}

	// 计算汇总统计
	summary := s.calculateChannelDueStatisticsSummary(basicStats, userTypeStats)

	return &ChannelDueStatisticsResponse{
		Statistics: summary,
		Pagination: listData,
	}
}

// calculateChannelDueStatisticsSummary 计算渠道到期统计汇总
func (s *Service) calculateChannelDueStatisticsSummary(
	basicStats []BasicBillStats,
	userTypeStats []UserTypeStats,
) ChannelDueStatisticsSummary {
	var summary ChannelDueStatisticsSummary

	// 汇总基础统计
	var totalBills int
	var totalDueAmount, totalPaidAmount float64

	for _, stat := range basicStats {
		totalBills += stat.TotalBills
		// 使用shopspringutils进行精确的金额累加
		totalDueAmount = shopspringutils.AddAmountsWithDecimal(totalDueAmount, stat.TotalDueAmount)
		totalPaidAmount = shopspringutils.AddAmountsWithDecimal(totalPaidAmount, stat.TotalPaidAmount)
	}

	// 汇总新老用户统计
	var newUserBills, oldUserBills int
	var newUserDueAmount, newUserPaidAmount float64
	var oldUserDueAmount, oldUserPaidAmount float64

	for _, stat := range userTypeStats {
		newUserBills += stat.NewUserBills
		oldUserBills += stat.OldUserBills
		// 使用shopspringutils进行精确的金额累加
		newUserDueAmount = shopspringutils.AddAmountsWithDecimal(newUserDueAmount, stat.NewUserDueAmount)
		newUserPaidAmount = shopspringutils.AddAmountsWithDecimal(newUserPaidAmount, stat.NewUserPaidAmount)
		oldUserDueAmount = shopspringutils.AddAmountsWithDecimal(oldUserDueAmount, stat.OldUserDueAmount)
		oldUserPaidAmount = shopspringutils.AddAmountsWithDecimal(oldUserPaidAmount, stat.OldUserPaidAmount)
	}

	// 构建汇总结果
	summary.TotalDueBills = totalBills
	summary.NewUserBills = newUserBills
	summary.OldUserBills = oldUserBills
	summary.TotalDueAmount = FormatAmount(totalDueAmount)
	summary.NewUserDueAmount = FormatAmount(newUserDueAmount)
	summary.OldUserDueAmount = FormatAmount(oldUserDueAmount)
	summary.TotalRepaymentRate = CalculateRepaymentRate(totalPaidAmount, totalDueAmount)
	summary.NewUserRepaymentRate = CalculateRepaymentRate(newUserPaidAmount, newUserDueAmount)
	summary.OldUserRepaymentRate = CalculateRepaymentRate(oldUserPaidAmount, oldUserDueAmount)

	return summary
}

// ExpenseDetailsResponse 支出明细响应结构
type ExpenseDetailsResponse struct {
	Statistics ExpenseStatistics              `json:"statistics"` // 统计数据
	List       *pagination.PaginationResponse `json:"list"`       // 列表数据
}

// ExpenseStatistics 支出统计结构
type ExpenseStatistics struct {
	TotalExpense   float64 `json:"total_expense"`    // 支出总额
	NewUserExpense float64 `json:"new_user_expense"` // 新用户支出总额
	OldUserExpense float64 `json:"old_user_expense"` // 旧用户支出总额
	TodayExpense   float64 `json:"today_expense"`    // 今日支出
	ExpenseCount   int     `json:"expense_count"`    // 支出笔数
	ExpenseOrders  int     `json:"expense_orders"`   // 支出订单数
	ExpenseUsers   int     `json:"expense_users"`    // 支出人数
}

// GetExpenseDetailsList 获取支出明细列表
func (s *Service) GetExpenseDetailsList(params map[string]interface{}) (resp *ExpenseDetailsResponse, err error) {

	resp = &ExpenseDetailsResponse{
		Statistics: ExpenseStatistics{},
		List:       &pagination.PaginationResponse{},
	}

	// 构建分页请求参数
	page := convert.GetIntFromMap(params, "page", 1)
	pageSize := convert.GetIntFromMap(params, "page_size", 10)

	paginationReq := pagination.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}
	paginationReq.ValidateAndNormalize()

	// 并行获取统计数据和列表数据
	var statistics *ExpenseStatistics
	var listData *pagination.PaginationResponse

	g, _ := errgroup.WithContext(s.ctx)

	// 并行获取统计数据
	g.Go(func() error {
		statistics, err = s.repository.GetExpenseDetailsStatistics(params)
		if err != nil {
			s.logger.Error("获取支出统计数据失败", log.String("error", err.Error()))
			return fmt.Errorf("获取支出统计数据失败: %v", err)
		}
		return nil
	})

	// 并行获取列表数据
	g.Go(func() error {
		listData, err = s.repository.GetExpenseDetailsList(params, paginationReq)
		if err != nil {
			s.logger.Error("获取支出明细列表失败", log.String("error", err.Error()))
			return fmt.Errorf("获取支出明细列表失败: %v", err)
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return
	}

	// 处理列表数据
	s.processExpenseDetailsList(listData)

	resp.Statistics = *statistics
	resp.List = listData

	s.logger.Info("获取支出明细列表成功",
		log.Int64("total", listData.Total),
		log.Int("page", listData.Page),
		log.Int("pageSize", listData.PageSize),
	)

	return
}

// processExpenseDetailsList 处理支出明细列表数据
func (s *Service) processExpenseDetailsList(result *pagination.PaginationResponse) {
	if result == nil || result.Data == nil {
		return
	}

	if dataSlice, ok := result.Data.([]gform.Data); ok {
		for i, item := range dataSlice {
			// 设置支付方式为固定值
			item["payment_method"] = "统统付小贷打款"

			// 格式化金额
			if principal, exists := item["principal"]; exists {
				if amount, ok := principal.(float64); ok {
					item["amount"] = FormatAmount(amount)
				}
			}

			// 格式化时间
			if disbursedAt, exists := item["disbursed_at"]; exists {
				if timeVal, ok := disbursedAt.(time.Time); ok {
					item["disbursed_at"] = timeVal.Format("2006-01-02 15:04:05")
				}
			}

			dataSlice[i] = item
		}
		result.Data = dataSlice
	}
}

// DueStatisticsResponse 到期统计响应结构
type DueStatisticsResponse struct {
	Statistics DueStatisticsData           `json:"statistics"` // 统计数据
	List       *pagination.PaginationResponse `json:"list"`       // 列表数据
}

// DueStatisticsData 到期统计数据结构
type DueStatisticsData struct {
	// 基础统计
	TotalBills        int     `json:"total_bills"`         // 到期账单数
	NewUserBills      int     `json:"new_user_bills"`      // 新用户账单数
	OldUserBills      int     `json:"old_user_bills"`      // 老用户账单数
	TotalDueAmount    string  `json:"total_due_amount"`    // 到期金额数
	TotalPrincipal    string  `json:"total_principal"`     // 到期成本
	NewUserDueAmount  string  `json:"new_user_due_amount"` // 新用户到期金额
	OldUserDueAmount  string  `json:"old_user_due_amount"` // 老用户到期金额

	// 还款统计
	RepaymentUsers           int    `json:"repayment_users"`            // 还款人数
	RepaymentRepurchaseUsers int    `json:"repayment_repurchase_users"` // 还款复购人数
	RepaymentEligibleUsers   int    `json:"repayment_eligible_users"`   // 还款可复购人数
	TotalRepaymentAmount     string `json:"total_repayment_amount"`     // 还款金额

	// 回款率统计
	TotalRepaymentRate    string `json:"total_repayment_rate"`     // 金额回款率
	NewUserRepaymentRate  string `json:"new_user_repayment_rate"`  // 新用户金额回款率
	OldUserRepaymentRate  string `json:"old_user_repayment_rate"`  // 老用户金额回款率
	RepurchaseRate        string `json:"repurchase_rate"`          // 复购率
}

// GetDueStatistics 获取到期统计
func (s *Service) GetDueStatistics(params map[string]interface{}) (resp DueStatisticsResponse, err error) {
	startTime := time.Now()
	s.logger.Info("开始获取到期统计", log.Any("params", params))

	// 获取分页参数
	paginationReq := pagination.PaginationRequest{
		Page:     convert.GetIntFromMap(params, "page", 1),
		PageSize: convert.GetIntFromMap(params, "page_size", 10),
	}
	paginationReq.ValidateAndNormalize()

	// 使用errgroup并行执行查询
	g, _ := errgroup.WithContext(s.ctx)

	var (
		statistics *DueStatisticsData
		listData   *pagination.PaginationResponse
	)

	// 并行获取统计数据
	g.Go(func() error {
		statistics, err = s.repository.GetDueStatisticsData(params)
		if err != nil {
			s.logger.Error("获取到期统计数据失败", log.String("error", err.Error()))
			return fmt.Errorf("获取到期统计数据失败: %v", err)
		}
		return nil
	})

	// 并行获取列表数据
	g.Go(func() error {
		listData, err = s.repository.GetDueStatisticsList(params, paginationReq)
		if err != nil {
			s.logger.Error("获取到期统计列表失败", log.String("error", err.Error()))
			return fmt.Errorf("获取到期统计列表失败: %v", err)
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return
	}

	// 处理列表数据
	s.processDueStatisticsList(listData)

	resp.Statistics = *statistics
	resp.List = listData

	s.logger.Info("获取到期统计成功",
		log.Int64("total", listData.Total),
		log.Int("page", listData.Page),
		log.Int("pageSize", listData.PageSize),
		log.String("duration", time.Since(startTime).String()),
	)

	return
}

// processDueStatisticsList 处理到期统计列表数据
func (s *Service) processDueStatisticsList(result *pagination.PaginationResponse) {
	if result == nil || result.Data == nil {
		return
	}

	if dataSlice, ok := result.Data.([]gform.Data); ok {
		for i, item := range dataSlice {
			// 在Go代码中进行计算，不在SQL中计算
			totalDueAmountRaw := gconv.Float64(item["total_due_amount_raw"])
			totalWaiveAmount := gconv.Float64(item["total_waive_amount"])
			totalDueAmount := shopspringutils.SubtractAmountsWithDecimal(totalDueAmountRaw, totalWaiveAmount)

			newUserDueAmountRaw := gconv.Float64(item["new_user_due_amount_raw"])
			newUserWaiveAmount := gconv.Float64(item["new_user_waive_amount"])
			newUserDueAmount := shopspringutils.SubtractAmountsWithDecimal(newUserDueAmountRaw, newUserWaiveAmount)

			oldUserDueAmountRaw := gconv.Float64(item["old_user_due_amount_raw"])
			oldUserWaiveAmount := gconv.Float64(item["old_user_waive_amount"])
			oldUserDueAmount := shopspringutils.SubtractAmountsWithDecimal(oldUserDueAmountRaw, oldUserWaiveAmount)

			paidAmount := gconv.Float64(item["paid_amount"])
			waiveAmountSum := gconv.Float64(item["waive_amount_sum"])
			repaymentAmount := shopspringutils.AddAmountsWithDecimal(paidAmount, waiveAmountSum)

			newUserPaidAmount := gconv.Float64(item["new_user_paid_amount"])
			newUserWaiveAmountSum := gconv.Float64(item["new_user_waive_amount_sum"])
			newUserRepaymentAmount := shopspringutils.AddAmountsWithDecimal(newUserPaidAmount, newUserWaiveAmountSum)

			oldUserPaidAmount := gconv.Float64(item["old_user_paid_amount"])
			oldUserWaiveAmountSum := gconv.Float64(item["old_user_waive_amount_sum"])
			oldUserRepaymentAmount := shopspringutils.AddAmountsWithDecimal(oldUserPaidAmount, oldUserWaiveAmountSum)

			// 计算逾期金额
			overdueAmount := shopspringutils.SubtractAmountsWithDecimal(totalDueAmount, repaymentAmount)

			// 设置计算后的金额字段
			item["total_due_amount"] = FormatAmount(totalDueAmount)
			item["new_user_due_amount"] = FormatAmount(newUserDueAmount)
			item["old_user_due_amount"] = FormatAmount(oldUserDueAmount)
			item["repayment_amount"] = FormatAmount(repaymentAmount)
			item["overdue_amount"] = FormatAmount(overdueAmount)

			// 格式化当日还款金额
			if todayRepaymentAmount, exists := item["today_repayment_amount"]; exists {
				if amount, ok := todayRepaymentAmount.(float64); ok {
					item["today_repayment_amount"] = FormatAmount(amount)
				}
			}

			// 计算回款率
			if shopspringutils.CompareAmountsWithDecimal(totalDueAmount, 0) > 0 {
				rate := shopspringutils.DivideAmountsWithDecimal(repaymentAmount, totalDueAmount)
				item["repayment_rate"] = fmt.Sprintf("%.2f", shopspringutils.CeilToTwoDecimal(shopspringutils.MultiplyAmountsWithDecimal(rate, 100)))
			} else {
				item["repayment_rate"] = "0.00"
			}

			if shopspringutils.CompareAmountsWithDecimal(newUserDueAmount, 0) > 0 {
				rate := shopspringutils.DivideAmountsWithDecimal(newUserRepaymentAmount, newUserDueAmount)
				item["new_user_repayment_rate"] = fmt.Sprintf("%.2f", shopspringutils.CeilToTwoDecimal(shopspringutils.MultiplyAmountsWithDecimal(rate, 100)))
			} else {
				item["new_user_repayment_rate"] = "0.00"
			}

			if shopspringutils.CompareAmountsWithDecimal(oldUserDueAmount, 0) > 0 {
				rate := shopspringutils.DivideAmountsWithDecimal(oldUserRepaymentAmount, oldUserDueAmount)
				item["old_user_repayment_rate"] = fmt.Sprintf("%.2f", shopspringutils.CeilToTwoDecimal(shopspringutils.MultiplyAmountsWithDecimal(rate, 100)))
			} else {
				item["old_user_repayment_rate"] = "0.00"
			}

			// 计算复购率
			repaymentEligibleUsers := gconv.Int(item["repayment_eligible_users"])
			repaymentRepurchaseUsers := gconv.Int(item["repayment_repurchase_users"])
			if repaymentEligibleUsers > 0 {
				rate := shopspringutils.DivideAmountsWithDecimal(float64(repaymentRepurchaseUsers), float64(repaymentEligibleUsers))
				item["repurchase_rate"] = fmt.Sprintf("%.2f", shopspringutils.CeilToTwoDecimal(shopspringutils.MultiplyAmountsWithDecimal(rate, 100)))
			} else {
				item["repurchase_rate"] = "0.00"
			}

			// 格式化日期字段
			if dueDate, exists := item["due_date"]; exists {
				if dateVal, ok := dueDate.(time.Time); ok {
					item["due_date"] = dateVal.Format("2006-01-02")
				}
			}

			dataSlice[i] = item
		}
		result.Data = dataSlice
	}
}
