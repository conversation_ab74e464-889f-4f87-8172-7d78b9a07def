import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const USERMANAGE: AppRouteRecordRaw = {
  path: '/usermanage',
  name: 'usermanage',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.usermanage',
    requiresAuth: true,
    icon: 'icon-user',
    order: 3,
  },
  children: [
    {
      path: 'list',
      name: 'UserManageList',
      component: () => import('@/views/usermanage/list/index.vue'),
      meta: {
        locale: 'menu.usermanage.list',
        requiresAuth: true,
        roles: ['*'],
      },
    },
    {
      path: 'userDetail/:id',
      name: 'UserManageDetail',
      component: () => import('@/views/usermanage/detail/index.vue'),
      meta: {
        locale: 'menu.usermanage.detail',
        requiresAuth: true,
        roles: ['*'],
        hideInMenu: true,
      },
    },
    {
      path: 'repurchase',
      name: 'UserManageRepurchase',
      component: () => import('@/views/usermanage/repurchase/index.vue'),
      meta: {
        locale: 'menu.usermanage.repurchase',
        requiresAuth: true,
        roles: ['*'],
      },
    },
  ],
};

export default USERMANAGE; 