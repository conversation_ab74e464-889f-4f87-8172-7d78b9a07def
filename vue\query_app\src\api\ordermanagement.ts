import { defHttp } from '@/utils/http';
import type { BaseResponse } from '@/api/base';

// 订单查询参数接口
export interface OrderQueryParams {
  order_no?: string; // 订单编号
  user_id?: number; // 用户ID
  product_rule_id?: number; // 产品规则ID
  loan_amount_min?: number; // 申请贷款金额最小值
  loan_amount_max?: number; // 申请贷款金额最大值
  channel_id?: number; // 渠道ID
  customer_origin?: string; // 客户来源
  initial_order_channel_id?: number; // 初始下单渠道ID
  payment_channel_id?: number; // 支付渠道ID
  status?: number; // 订单状态: 0-待放款, 1-放款中, 2-交易关闭, 3-交易完成
  is_freeze?: number; // 是否冻结: 0-否, 1-是
  is_refund_needed?: number; // 是否需要退款: 0-否, 1-是
  complaint_status?: number; // 投诉状态: 0-否, 1-是
  audit_assignee_id?: number; // 审核员ID
  review_status?: number; // 审核状态: 0-待审核, 1-审核通过, 2-审核拒绝
  sales_assignee_id?: number; // 跟进的业务员ID
  collection_assignee_id?: number; // 当前催收员ID
  submitted_at_start?: string; // 下单时间开始范围 (格式: YYYY-MM-DD HH:mm:ss)
  submitted_at_end?: string; // 下单时间结束范围 (格式: YYYY-MM-DD HH:mm:ss)
  disbursed_at_start?: string; // 放款时间开始范围 (格式: YYYY-MM-DD HH:mm:ss)
  disbursed_at_end?: string; // 放款时间结束范围 (格式: YYYY-MM-DD HH:mm:ss)
  completed_at_start?: string; // 结清/关闭时间开始范围 (格式: YYYY-MM-DD HH:mm:ss)
  completed_at_end?: string; // 结清/关闭时间结束范围 (格式: YYYY-MM-DD HH:mm:ss)
  page?: number; // 页码
  page_size?: number; // 每页数量
  user_name?: string; // 用户姓名
  user_id_card?: string; // 用户身份证号
  user_mobile?: string; // 用户手机号
  audit_assignee_name?: string; // 审核人姓名
  sales_assignee_name?: string; // 审核人姓名
  reason_for_closure?: number; // 关单原因
  bill_due_date_start?: string,
  bill_due_date_end?: string,
}

// 订单列表项接口
export interface OrderListItem {
  id: number; // 订单ID
  order_no: string; // 订单编号
  user_id: number; // 用户ID
  product_rule_id: number; // 产品规则ID
  loan_amount: number; // 申请贷款金额
  principal: number; // 实际放款本金
  total_interest: number; // 总利息
  total_guarantee_fee: number; // 总担保费
  total_other_fees: number; // 总其他费用
  total_repayable_amount: number; // 总应还金额
  amount_paid: number; // 已还金额
  channel_name: string; // 渠道名称
  customer_origin: string; // 客户来源
  initial_order_channel_name: string; // 初始订单渠道名称
  payment_channel_name: string; // 支付渠道名称
  status: number; // 订单状态: 0-待放款, 1-放款中, 2-交易关闭, 3-交易完成
  is_freeze: number; // 是否冻结: 0-否, 1-是
  complaint_status: number; // 投诉状态: 0-否, 1-是
  audit_assignee_id: number; // 审核员ID
  review_status: number; // 审核状态: 0-待审核, 1-审核通过, 2-审核拒绝
  sales_assignee_id: number; // 业务员ID
  collection_assignee_id: number; // 催收员ID
  created_at: string; // 创建时间
  updated_at: string; // 更新时间
  disbursed_at: string; // 放款时间
  user_name: string; // 用户名
  user_mobile: string; // 用户手机号
  user_id_card: string; // 用户身份证
  product_name: string; // 产品名称
  loan_period: number; // 借款周期(天)
  total_periods: number; // 总期数
  audit_assignee_name: string; // 审核员名称
  sales_assignee_name: string; // 业务员名称
  collection_assignee_name: string; // 催收员名称
  reason_for_closure?: number; // 关单类型
  reason_for_closure_text?: string; // 关单原因文本
  has_complaint: number; // 是否有投诉
  risk_score?: number; // 风控分数
}

// 分页响应接口
export interface PaginationResponse {
  total: number; // 总记录数
  page: number; // 当前页码
  pageSize: number; // 每页数量
  totalPages: number; // 总页数
  hasNext: boolean; // 是否有下一页
  hasPrev: boolean; // 是否有上一页
  data: OrderListItem[]; // 数据列表
}

// 修改订单渠道请求接口
export interface UpdateOrderChannelRequest {
  order_id?: number; // 订单ID
  channel_id: number; // 新的渠道ID
}

// 下单人信息接口
export interface OrderCustomerInfo {
  name: string; // 姓名
  repeat_buy_count: number; // 复购次数
  age: number; // 年龄
  in_progress_orders: number; // 在途订单数
  completed_orders: number; // 完结订单数
  location: string; // 所在位置
  mobile: string; // 手机号
  risk_score: number; // 风控评分
  gender: string; // 性别
  total_orders: number; // 全部订单数
  first_login_ip: string; // 首次登录IP
  id_card_front_url: string; // 身份证正面URL
  id_card_back_url: string; // 身份证反面URL
  id_card_no: string; // 身份证号
  order_time: string; // 下单时间
  order_status: number; // 订单状态
  last_login_ip: string; // 最后登录IP
  last_login_location: string; // 最后登录位置
  payment_channel: string; // 支付渠道
  order_no: string; // 订单编号
  loan_amount: number; // 申请金额
  user_status: number; // 用户状态
  register_time: string; // 注册时间
  past_quota: number; // 历史额度
  all_quota: number; // 总额度
  reminder_quota: number; // 剩余额度
  review_status: number; // 复审状态：0-未复审，1-复审通过，2-复审拒绝
  review_remark: string; // 复审备注
  emergency_contact_0?: {
    name: string; // 联系人姓名
    phone: string; // 联系人电话
    relation: string; // 联系人关系
  };
  emergency_contact_1?: {
    name: string; // 联系人姓名
    phone: string; // 联系人电话
    relation: string; // 联系人关系
  };
  remarks?: {
    id: number; // 备注ID
    content: string; // 备注内容
    user_id: number; // 用户ID
    user_name: string; // 用户名称
    create_time: string; // 创建时间
  }[];
}

// 订单分配请求接口
export interface OrderAssignRequest {
  order_ids: number[]; // 订单ID数组
  sales_id: number; // 业务员ID
  operator_id?: number; // 操作员ID，可选
}

// 订单分配响应接口
export interface OrderAssignResponse {
  failure_count: number; // 失败数量
  success_count: number; // 成功数量
  total_count: number; // 总数量
  results: Array<{
    order_id: number; // 订单ID
    success: boolean; // 是否成功
    message: string; // 消息
  }>;
}
export interface GetOrderPaymentRecordsParams {
  order_no: string,
  page: number,
  pageSize: number,
}
// API接口枚举
enum Api {
  ListOrders = '/order/manager/listOrders',
  GetOrderStatus = '/order/manager/getOrderStatus',
  CloseOrder = '/order/manager/closeOrder',
  ProcessDisbursement = '/order/manager/processDisbursement',
  AssignOrder = '/order/manager/assignOrder',
  ClaimOrder = '/order/manager/claimOrder',
  ManualReview = '/order/manager/manualReview',
  GetPendingOrders = '/order/manager/getPendingOrders',
  UpdateOrderChannel = '/order/manager/updateOrderChannel',
  GetOrderCustomerInfo = '/order/manager/getOrderCustomerInfo',
  CreateOrderRemark = '/order/manager/createOrderRemark',
  EarlySettlement = '/order/manager/earlySettlement',
  GetOrderBillInfo = '/order/manager/getOrderBillInfo',
  GetOrderPaymentRecords = '/order/manager/getOrderPaymentRecords',
  UpdateBillDueDate = '/order/manager/updateBillDueDate',
  WaiveBillAmount = '/order/manager/waiveBillAmount',
  ProcessPartialOfflinePayment = '/payment/manager/processPartialOfflinePayment',
  GetOrderProgress = '/order/manager/getOrderProgress',
  ManualWithhold = '/repayment/manager/manualWithhold',
  GetSubmittedAmounts = '/repayment/manager/getSubmittedAmounts',
  GetPaymentStatus = '/repayment/manager/getPaymentStatus',
  CancelOfflinePayment = '/payment/manager/cancelOfflinePayment',
  ProcessRefund = '/payment/manager/processRefund',
  RefreshRefundStatus = '/payment/manager/refreshRefundStatus',
  GetDownloadContract = '/order/manager/getDownloadContract',
  ListDueBills = '/order/manager/listDueBills',
  ListOverdueBills = '/order/manager/listOverdueBills',
  RecordCollection = '/order/manager/recordCollection',
  ListCollection = '/order/manager/listCollection',
  DistributeCollection = '/order/manager/distributeCollection',
}

// 获取订单列表
export function getOrderList(params?: OrderQueryParams) {
  return defHttp.post<PaginationResponse>({
    url: Api.ListOrders,
    data: params,
  });
}

// 获取订单状态
export function getOrderStatus(orderNo: string) {
  return defHttp.get<BaseResponse<any>>({
    url: `${Api.GetOrderStatus}`,
    params: { orderNo },
  });
}

// 关闭订单
export function closeOrder(orderNo: string, reason: string) {
  return defHttp.post<BaseResponse<any>>({
    url: `${Api.CloseOrder}/${orderNo}`,
    data: { reason },
  });
}

// 处理订单放款
export function processDisbursement(orderNo: string) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.ProcessDisbursement,
    data: { orderNo },
  });
}

// 修改订单渠道
export function updateOrderChannel(orderId: number, channelId: number) {
  return defHttp.post<BaseResponse<any>>({
    url: `${Api.UpdateOrderChannel}`,
    data: { 
      order_id: orderId,
      channel_id: channelId 
    },
  });
}

// 获取下单人信息
export function getOrderCustomerInfo(orderId: number | string) {
  return defHttp.get<BaseResponse<OrderCustomerInfo>>({
    url: Api.GetOrderCustomerInfo,
    params: { orderId },
  });
}

// 订单备注创建请求接口
export interface OrderRemarkCreateRequest {
  order_id: number; // 订单ID
  content: string; // 备注内容
}

// 创建订单备注
export function createOrderRemark(orderId: number, content: string) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.CreateOrderRemark,
    data: { 
      order_id: orderId,
      content: content 
    },
  });
}

// 提前结清订单
export function earlySettlement(orderNo: string) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.EarlySettlement,
    data: { orderNo },
  });
}

// 分配订单
export function assignOrder(orderIds: number[], salesId: number, operatorId: number = 0) {
  return defHttp.post<OrderAssignResponse>({
    url: Api.AssignOrder,
    data: { 
      order_ids: orderIds,
      sales_id: salesId,
      operator_id: operatorId
    },
  });
}

// 认领订单
export function claimOrder(orderId: number, salesId: number) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.ClaimOrder,
    data: { 
      orderId,
      salesId
    },
  });
}

// 获取订单详情页账单信息
export function getOrderBillInfo(order_no: number | string) {
  return defHttp.get<BaseResponse<any>>({
    url: Api.GetOrderBillInfo,
    params: { order_no },
  });
}

// 获取订单支付记录列表
//   GET /business/order/manager/getOrderPaymentRecords
//   接口ID：325899384
//   接口地址：https://app.apifox.com/link/project/6604289/apis/api-325899384
export function getOrderPaymentRecords(params: GetOrderPaymentRecordsParams) {
  return defHttp.get<BaseResponse<any>>({
    url: Api.GetOrderPaymentRecords,
    params: params,
  });
}

// 修改账单时间
export function updateBillDueDate(bill_id: number, due_date: string) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.UpdateBillDueDate,
    data: {
      bill_id,
      due_date
    },
  });
}
// 减免账单金额
export function waiveBillAmount(bill_id: number, waive_amount: number) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.WaiveBillAmount,
    data: {
      bill_id,
      waive_amount
    },
  });
}

// 部分支付
export function processPartialOfflinePayment(params: {
  bill_id: number;
  payment_channel: string;
  voucher: string;
  amount: number;
}) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.ProcessPartialOfflinePayment,
    data: params,
  });
}

// 获取订单进度 /business/order/manager/getOrderProgress
export function getOrderProgress(order_no: string) {
  return defHttp.get<BaseResponse<any>>({
    url: Api.GetOrderProgress,
    params: { order_no },
  });
}

// 代扣/管理员帮还 - /business/repayment/manager/manualWithhold
export function manualWithhold(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.ManualWithhold,
    data: params,
  });
}
//账单已提交的担保与资管总和 /business/repayment/manager/getSubmittedAmounts
export function getSubmittedAmounts(bill_id: number) {
  return defHttp.get<BaseResponse<any>>({
    url: Api.GetSubmittedAmounts,
    params: { bill_id },
  });
}

// 查询支付状态 /business/repayment/manager/getPaymentStatus
export function getPaymentStatus(transaction_no: string) {
  return defHttp.get<BaseResponse<any>>({
    url: Api.GetPaymentStatus,
    params: { transaction_no },
  });
}
// 销账取消 /business/payment/manager/cancelOfflinePayment
export function cancelOfflinePayment(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.CancelOfflinePayment,
    data: params,
  });
}
// 客户退款 /business/payment/manager/processRefund
export function processRefund(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.ProcessRefund,
    data: params,
  });
}
// 退款刷新 /business/payment/manager/refreshRefundStatus
export function refreshRefundStatus(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.RefreshRefundStatus,
    data: params,
  });
}
// 下载合同 /business/order/manager/getDownloadContract
export function getDownloadContract(params: any) {
  return defHttp.get<BaseResponse<any>>({
    url: Api.GetDownloadContract,
    params: params,
  });
}

// 到期账单列表 /business/order/manager/listDueBills
export function listDueBills(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.ListDueBills,
    data: params
  },{
    isTransformResponse: false,
  });
}

// 逾期账单列表 /business/order/manager/listOverdueBills
export function listOverdueBills(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.ListOverdueBills,
    data: params,
  });
}

// 记录催收 /business/order/manager/recordCollection
export function recordCollection(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.RecordCollection,
    data: params,
  });
}

// 记录催收详情 /business/order/manager/listCollection
export function listCollection(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.ListCollection,
    data: params,
  });
}

// 分配催收 /business/order/manager/distributeCollection
export function distributeCollection(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.DistributeCollection,
    data: params,
  });
}


