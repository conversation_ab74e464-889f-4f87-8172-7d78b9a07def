import { defHttp } from '@/utils/http';

// 基础API响应接口
export interface BaseResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 首页统计数据接口
export interface HomeStatistics {
  disbursement_amount: number;
  disbursement_customer_count: number;
  disbursement_order_count: number;
  due_amount: number;
  due_repayment_amount: number;
  due_repayment_rate: number;
  due_funds_recovery_rate: number;
  overdue_customer_count: number;
  overdue_amount: number;
}
// 首页趋势图数据接口
export interface TrendStatistics {

}

enum Api {
  GetHomeStatistics = '/statistics/statisticscontroller/getHomeStatistics',
  GetTrendStatistics = '/statistics/statisticscontroller/getTrendStatistics',
}

export function getHomeStatistics(
  date_begin: string = '',
  date_end: string = ''
) {
  return defHttp.get<BaseResponse<HomeStatistics>>(
    {
      url: Api.GetHomeStatistics,
      params: {
        date_begin: date_begin ? date_begin : null,
        date_end: date_end ? date_end : null,
      },
    },
    {
      errorMessageMode: 'message',
    }
  );
}

// 首页趋势图数据接口
export function getTrendStatistics(days: number = 7) {
  return defHttp.get<BaseResponse<TrendStatistics>>(
    {
      url: Api.GetTrendStatistics,
      params: {
        days
      },
    },
    {
      errorMessageMode: 'message',
    }
  );
}
