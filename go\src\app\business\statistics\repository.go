package statistics

import (
	"context"
	"fmt"
	"strings"
	"time"

	"fincore/app/business/payment"
	"fincore/model"
	"fincore/utils/gform"
	"fincore/utils/pagination"
	"fincore/utils/shopspringutils"

	"github.com/gogf/gf/v2/util/gconv"
	"github.com/golang-module/carbon/v2"
)

// Repository 渠道统计数据仓库层
type Repository struct {
	ctx context.Context
}

// NewRepository 创建渠道统计数据仓库实例
func NewRepository(ctx context.Context) *Repository {
	return &Repository{
		ctx: ctx,
	}
}

// GetEnabledChannels 获取所有启用的渠道
func (r *Repository) GetEnabledChannels() ([]gform.Data, error) {
	result, err := model.DB(model.WithContext(r.ctx)).Table("channel").
		Where("channel_status", 1).
		Get()

	if err != nil {
		return nil, fmt.Errorf("查询启用渠道列表失败: %v", err)
	}

	return result, nil
}

// GetNewUserCountByChannel 统计渠道当天新用户数
func (r *Repository) GetNewUserCountByChannel(channelID uint, startTime, endTime time.Time) (uint, error) {
	// 转换时间为Unix时间戳
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	result, err := model.DB(model.WithContext(r.ctx)).Table("business_app_account").
		Where("channelId", "=", channelID).
		Where("createtime", ">=", startTimestamp).
		Where("createtime", "<", endTimestamp).
		Count()

	if err != nil {
		return 0, fmt.Errorf("统计渠道新用户数失败: %v", err)
	}

	return uint(result), nil
}

// GetRealNameCountByChannel 统计渠道当天实名通过数
func (r *Repository) GetRealNameCountByChannel(channelID uint, startTime, endTime time.Time) (uint, error) {

	result, err := model.DB(model.WithContext(r.ctx)).Table("business_app_account").
		Where("channelId", "=", channelID).
		// identityStatus 值为 2 表示通过
		Where("identityStatus", "=", 2).
		Where("identitySuccessTime", ">=", startTime).
		Where("identitySuccessTime", "<", endTime).
		Count()

	if err != nil {
		return 0, fmt.Errorf("统计渠道实名通过数失败: %v", err)
	}

	return uint(result), nil
}

// GetTransactionCountByChannel 统计渠道当天成交数
func (r *Repository) GetTransactionCountByChannel(channelID uint, startTime, endTime time.Time) (uint, error) {
	// 统计成功放款的订单数
	result, err := model.DB(model.WithContext(r.ctx)).Table("business_loan_orders").
		Where("channel_id", "=", channelID).
		WhereIn("status", []interface{}{model.OrderStatusDisbursed, model.OrderStatusCompleted}).
		Where("disbursed_at", ">=", startTime).
		Where("disbursed_at", "<", endTime).
		Count()

	if err != nil {
		return 0, fmt.Errorf("统计渠道成交数失败: %v", err)
	}

	return uint(result), nil
}

// SaveChannelStatistics 保存渠道统计数据
func (r *Repository) SaveChannelStatistics(channelID uint, newUserCount, realNameCount, transactionCount uint, date carbon.Carbon) error {
	// 获取当前日期
	currentDay := date.Format("Y-m-d")

	// 检查今天是否已有统计记录
	existing, err := model.DB(model.WithContext(r.ctx)).Table("channel_statistics").
		Where("channel_id", "=", channelID).
		Where("DATE(created_at)", "=", currentDay).
		First()

	if err != nil {
		return fmt.Errorf("查询现有统计记录失败: %v", err)
	}

	data := map[string]interface{}{
		"channel_id":             channelID,
		"new_customer_reg_num":   newUserCount,
		"real_name_num":          realNameCount,
		"number_of_transactions": transactionCount,
		"created_at":             time.Now(),
	}

	if existing == nil {
		// 新增记录
		_, err = model.DB(model.WithContext(r.ctx)).Table("channel_statistics").Insert(data)
		if err != nil {
			return fmt.Errorf("插入渠道统计数据失败: %v", err)
		}
	} else {
		// 更新记录 - 移除created_at字段
		updateData := map[string]interface{}{
			"new_customer_reg_num":   newUserCount,
			"real_name_num":          realNameCount,
			"number_of_transactions": transactionCount,
			"updated_at":             time.Now(),
		}
		_, err = model.DB(model.WithContext(r.ctx)).Table("channel_statistics").
			Where("channel_id", "=", channelID).
			Where("DATE(created_at)", "=", currentDay).
			Update(updateData)
		if err != nil {
			return fmt.Errorf("更新渠道统计数据失败: %v", err)
		}
	}

	return nil
}

// ChannelInfo 渠道信息结构体
type ChannelInfo struct {
	ID          uint   `json:"id"`           // 渠道IDs
	ChannelName string `json:"channel_name"` // 渠道名称
	ChannelCode string `json:"channel_code"` // 渠道编码
}

// ConvertToChannelInfo 将map数据转换为ChannelInfo结构体
func (r *Repository) ConvertToChannelInfo(data map[string]interface{}) (*ChannelInfo, error) {
	var channelInfo ChannelInfo
	err := gconv.Struct(data, &channelInfo)
	if err != nil {
		return nil, fmt.Errorf("转换渠道信息失败: %v", err)
	}
	return &channelInfo, nil
}

// GetChannelStatisticsListByParams 根据参数查询渠道统计列表
func (r *Repository) GetChannelStatisticsListByParams(params map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	// 构建基础查询条件
	countQuery := r.buildChannelStatisticsQuery(params)
	dataQuery := r.buildChannelStatisticsQuery(params).
		Fields("cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at,cs.updated_at").
		OrderBy("cs.created_at DESC")

	// 使用公共分页工具执行查询
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, paginationReq)
	if err != nil {
		return nil, fmt.Errorf("查询渠道统计列表失败: %v", err)
	}

	return result, nil
}

// buildChannelStatisticsQuery 构建渠道统计查询条件
func (r *Repository) buildChannelStatisticsQuery(params map[string]interface{}) gform.IOrm {
	query := model.DB(model.WithContext(r.ctx)).Table("channel_statistics cs").
		LeftJoin("channel c", "cs.channel_id = c.id").
		Where("cs.deleted_at IS NULL")

	// 日期筛选
	if date, ok := params["date"]; ok && date != "" {
		dateStr := date.(string)
		query = query.Where("DATE(cs.created_at)", "=", dateStr)
	}

	return query
}

// DisbursementStatistics 放款统计结构体
type DisbursementStatistics struct {
	DisbursementAmount          float64 `json:"disbursement_amount"`           // 放款金额
	DisbursementPrincipalAmount float64 `json:"disbursement_principal_amount"` // 放款本金
	DisbursementCustomerCount   int     `json:"disbursement_customer_count"`   // 放款客户数
	DisbursementOrderCount      int     `json:"disbursement_order_count"`      // 放款订单数
}

// DueStatistics 到期统计结构体
type DueStatistics struct {
	DueAmount          float64 `json:"due_amount"`           // 到期金额
	DueRepaymentAmount float64 `json:"due_repayment_amount"` // 到期回款总额
	DuePrincipalAmount float64 `json:"due_principal_amount"` // 到期本金总额
}

// OverdueStatistics 逾期统计结构体
type OverdueStatistics struct {
	OverdueCustomerCount int     `json:"overdue_customer_count"` // 逾期客户数
	OverdueAmount        float64 `json:"overdue_amount"`         // 逾期总额
}

// TrendDataPoint 趋势数据点结构
type TrendDataPoint struct {
	Date               string  `json:"date"`                // 日期 YYYY-MM-DD
	DisbursementAmount float64 `json:"disbursement_amount"` // 放款金额(申请金额 - 前置利息 - 前置本金 - 前置担保费)
	RepaymentAmount    float64 `json:"repayment_amount"`    // 回款金额
	DueAmount          float64 `json:"due_amount"`          // 到期金额
	RepaymentRate      float64 `json:"repayment_rate"`      // 回款率
	RegistrationCount  int     `json:"registration_count"`  // 注册量
}

// GetDisbursementStatistics 获取放款统计数据
func (r *Repository) GetDisbursementStatistics(dateBegin, dateEnd time.Time) (*DisbursementStatistics, error) {
	query := model.DB(model.WithContext(r.ctx)).Table("business_payment_transactions")

	dateBeginTime := dateBegin.Format("2006-01-02")
	dateEndTime := dateEnd.Format("2006-01-02")
	// 累计数据：所有已放款的订单
	query = query.Where("status", model.TransactionStatusSuccess).Where("type", model.TransactionTypeDisbursement)
	if !dateBegin.IsZero() && !dateEnd.IsZero() {
		// 特定日期：该日期放款的订单
		query = query.WhereBetween("DATE(completed_at)", []interface{}{dateBeginTime, dateEndTime})
	}

	list, err := query.Get()
	if err != nil {
		return nil, fmt.Errorf("查询放款金额和订单数失败: %v", err)
	}
	// 查询放款金额和放款订单数
	amountResult, err := query.Fields("COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count").First()
	if err != nil {
		return nil, fmt.Errorf("查询放款金额和订单数失败: %v", err)
	}

	// 查询放款客户数
	customerQuery := query.Fields("COUNT(DISTINCT user_id) as customer_count")
	customerResult, err := customerQuery.First()
	if err != nil {
		return nil, fmt.Errorf("查询放款客户数失败: %v", err)
	}

	// 相关订单 id
	orderIds := make([]interface{}, 0)
	for _, order := range list {
		orderIds = append(orderIds, order["order_id"])
	}

	// 查询放款本金
	orderQuery := model.DB(model.WithContext(r.ctx)).Table("business_loan_orders")
	if len(orderIds) > 0 {
		orderQuery = orderQuery.WhereIn("id", orderIds)
	}
	if !dateBegin.IsZero() && !dateEnd.IsZero() {
		orderQuery = orderQuery.WhereBetween("DATE(disbursed_at)", []interface{}{dateBeginTime, dateEndTime})
	}

	orderResult, err := orderQuery.Fields("COALESCE(SUM(principal), 0) as principal_amount").First()
	if err != nil {
		return nil, fmt.Errorf("查询放款本金失败: %v", err)
	}

	return &DisbursementStatistics{
		DisbursementAmount:          gconv.Float64(amountResult["total_amount"]),
		DisbursementPrincipalAmount: gconv.Float64(orderResult["principal_amount"]),
		DisbursementOrderCount:      gconv.Int(amountResult["order_count"]),
		DisbursementCustomerCount:   gconv.Int(customerResult["customer_count"]),
	}, nil
}

// GetDueStatistics 获取到期统计数据
func (r *Repository) GetDueStatistics(dateBegin, dateEnd time.Time) (*DueStatistics, error) {
	query := model.DB(model.WithContext(r.ctx)).Table("business_repayment_bills")

	dateBeginTime := dateBegin.Format("2006-01-02")
	dateEndTime := dateEnd.Format("2006-01-02")
	if dateBegin.IsZero() || dateEnd.IsZero() {
		// 累计数据：所有到期日期 <= 今天的账单
		today := carbon.Now().Format("Y-m-d")
		query = query.Where("DATE(due_date)", "<=", today)
	} else {
		// 特定日期：该日期到期的账单
		query = query.WhereBetween("DATE(due_date)", []interface{}{dateBeginTime, dateEndTime})
	}

	// 查询到期金额和本金总额
	// 到期金额不包含减免金额
	totalResult, err := query.Fields(
		"COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount",
		"COALESCE(SUM(due_principal), 0) as due_principal_amount",
	).First()
	if err != nil {
		return nil, fmt.Errorf("查询到期金额失败: %v", err)
	}

	// 查询到期回款总额
	repaymentResult, err := query.Fields("COALESCE(SUM(paid_amount), 0) as repayment_amount").First()
	if err != nil {
		return nil, fmt.Errorf("查询到期回款总额失败: %v", err)
	}

	return &DueStatistics{
		DueAmount:          gconv.Float64(totalResult["due_amount"]),
		DuePrincipalAmount: gconv.Float64(totalResult["due_principal_amount"]),
		DueRepaymentAmount: gconv.Float64(repaymentResult["repayment_amount"]),
	}, nil
}

// GetOverdueStatistics 获取逾期统计数据
func (r *Repository) GetOverdueStatistics() (*OverdueStatistics, error) {
	// 截至今天
	cutoffDate := carbon.Now().Format("Y-m-d")
	// 查询逾期账单：到期日期 < 截止日期 且 状态为逾期相关
	overdueQuery := model.DB(model.WithContext(r.ctx)).Table("business_repayment_bills").
		Where("DATE(due_date)", "<", cutoffDate).
		WhereIn("status", []interface{}{
			model.RepaymentBillStatusOverdueUnpaid,
			model.RepaymentBillStatusOverduePartialPaid,
			model.RepaymentBillStatusOverduePaid,
		})

	// 查询逾期客户数
	customerResult, err := overdueQuery.Fields("COUNT(DISTINCT user_id) as customer_count").First()
	if err != nil {
		return nil, fmt.Errorf("查询逾期客户数失败: %v", err)
	}

	// 查询逾期总额：(total_due_amount - paid_amount - total_waive_amount)
	amountResult, err := overdueQuery.Fields(
		"COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount",
	).First()
	if err != nil {
		return nil, fmt.Errorf("查询逾期总额失败: %v", err)
	}

	return &OverdueStatistics{
		OverdueCustomerCount: gconv.Int(customerResult["customer_count"]),
		OverdueAmount:        gconv.Float64(amountResult["overdue_amount"]),
	}, nil
}

// GetDisbursementTrendData 获取放款金额趋势数据
func (r *Repository) GetDisbursementTrendData(days int) ([]TrendDataPoint, error) {
	// 构建SQL查询，获取近N日的放款金额趋势
	sql := `
		SELECT
			DATE(completed_at) as date,
			COALESCE(SUM(amount), 0) as disbursement_amount
		FROM business_payment_transactions
		WHERE status = ?
			AND type = ?
			AND DATE(completed_at) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
			AND DATE(completed_at) < CURDATE()
		GROUP BY DATE(completed_at)
		ORDER BY date
	`

	result, err := model.DB(model.WithContext(r.ctx)).Query(sql,
		model.TransactionStatusSuccess, model.TransactionTypeDisbursement, days)
	if err != nil {
		return nil, fmt.Errorf("查询放款金额趋势数据失败: %v", err)
	}

	var trendData []TrendDataPoint
	for _, row := range result {
		date := carbon.Parse(gconv.String(row["date"])).Format("Y-m-d")
		amount := gconv.Float64(row["disbursement_amount"])

		trendData = append(trendData, TrendDataPoint{
			Date:               date,
			DisbursementAmount: amount,
		})
	}

	return trendData, nil
}

// GetRepaymentTrendData 获取回款金额趋势数据
// 查询账单表，按到期日期统计已还金额，与账单状态无关
func (r *Repository) GetRepaymentTrendData(days int) ([]TrendDataPoint, error) {
	// 构建SQL查询，获取近N日按到期日期统计的已还金额趋势
	// 按账单到期日期分组，统计该日期对应的所有已还金额
	sql := `
		SELECT
			DATE(due_date) as date,
			COALESCE(SUM(paid_amount), 0) as repayment_amount
		FROM business_repayment_bills
		WHERE paid_amount > 0
			AND due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
			AND due_date < CURDATE()
		GROUP BY DATE(due_date)
		ORDER BY date
	`

	result, err := model.DB(model.WithContext(r.ctx)).Query(sql, days)
	if err != nil {
		return nil, fmt.Errorf("查询回款金额趋势数据失败: %v", err)
	}

	var trendData []TrendDataPoint
	for _, row := range result {
		date := carbon.Parse(gconv.String(row["date"])).Format("Y-m-d")
		amount := gconv.Float64(row["repayment_amount"])

		trendData = append(trendData, TrendDataPoint{
			Date:            date,
			RepaymentAmount: amount,
		})
	}

	return trendData, nil
}

// GetDueAmountTrendData 获取到期金额趋势数据
// 查询账单表按到期日期统计应还总额
func (r *Repository) GetDueAmountTrendData(days int) ([]TrendDataPoint, error) {
	// 构建SQL查询，获取近N日的到期金额趋势
	sql := `
		SELECT
			DATE(due_date) as date,
			COALESCE(SUM(total_due_amount), 0) as due_amount
		FROM business_repayment_bills
		WHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
			AND due_date < CURDATE()
		GROUP BY DATE(due_date)
		ORDER BY date
	`

	result, err := model.DB(model.WithContext(r.ctx)).Query(sql, days)
	if err != nil {
		return nil, fmt.Errorf("查询到期金额趋势数据失败: %v", err)
	}

	var trendData []TrendDataPoint
	for _, row := range result {
		date := carbon.Parse(gconv.String(row["date"])).Format("Y-m-d")
		amount := gconv.Float64(row["due_amount"])

		trendData = append(trendData, TrendDataPoint{
			Date:      date,
			DueAmount: amount,
		})
	}

	return trendData, nil
}

// GetRegistrationTrendData 获取注册量趋势数据
func (r *Repository) GetRegistrationTrendData(days int) ([]TrendDataPoint, error) {
	// 构建SQL查询，获取近N日的注册量趋势
	sql := `
		SELECT
			DATE(FROM_UNIXTIME(createtime)) as date,
			COUNT(*) as registration_count
		FROM business_app_account
		WHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))
			AND createtime < UNIX_TIMESTAMP(CURDATE())
		GROUP BY DATE(FROM_UNIXTIME(createtime))
		ORDER BY date
	`

	result, err := model.DB(model.WithContext(r.ctx)).Query(sql, days)
	if err != nil {
		return nil, fmt.Errorf("查询注册量趋势数据失败: %v", err)
	}

	var trendData []TrendDataPoint
	for _, row := range result {
		date := carbon.Parse(gconv.String(row["date"])).Format("Y-m-d")
		count := gconv.Int(row["registration_count"])

		trendData = append(trendData, TrendDataPoint{
			Date:              date,
			RegistrationCount: count,
		})
	}

	return trendData, nil
}

// GetIncomeDetailsStatistics 获取收入明细统计数据
func (r *Repository) GetIncomeDetailsStatistics(params map[string]interface{}) (*IncomeStatistics, error) {
	// 构建基础查询
	baseQuery := r.buildIncomeDetailsQuery(params)

	// 执行统计查询
	result, err := baseQuery.Fields(`
		SUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') THEN pt.amount ELSE 0 END) as total_income,
		SUM(CASE WHEN pt.type = 'REFUND' THEN pt.amount ELSE 0 END) as total_refund,
		SUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')
			 AND DATE(pt.completed_at) = rb.due_date THEN pt.amount ELSE 0 END) as due_income,
		SUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')
			 AND DATE(pt.completed_at) > rb.due_date THEN pt.amount ELSE 0 END) as overdue_income,
		SUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')
			 AND DATE(pt.completed_at) < rb.due_date THEN pt.amount ELSE 0 END) as early_income,
		COUNT(DISTINCT pt.user_id) as total_customers,
		COUNT(DISTINCT pt.order_id) as total_orders
	`).First()

	if err != nil {
		return nil, fmt.Errorf("查询收入统计数据失败: %v", err)
	}

	statistics := &IncomeStatistics{
		TotalIncome:    gconv.Float64(result["total_income"]),
		TotalRefund:    gconv.Float64(result["total_refund"]),
		DueIncome:      gconv.Float64(result["due_income"]),
		OverdueIncome:  gconv.Float64(result["overdue_income"]),
		EarlyIncome:    gconv.Float64(result["early_income"]),
		TotalCustomers: gconv.Int(result["total_customers"]),
		TotalOrders:    gconv.Int(result["total_orders"]),
	}

	return statistics, nil
}

// GetIncomeDetailsList 获取收入明细列表
func (r *Repository) GetIncomeDetailsList(params map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	// 构建查询条件
	countQuery := r.buildIncomeDetailsQuery(params)
	dataQuery := r.buildIncomeDetailsQuery(params).
		Fields(`
			pt.id,
			pt.transaction_no,
			lo.order_no,
			aa.name as user_name,
			aa.mobile,
			pt.type,
			pt.withhold_type,
			pt.offline_payment_channel_detail,
			pt.amount,
			rb.period_number,
			pt.completed_at,
			rb.due_date
		`).
		OrderBy("pt.id DESC")

	// 使用公共分页工具执行查询
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, paginationReq)
	if err != nil {
		return nil, fmt.Errorf("查询收入明细列表失败: %v", err)
	}

	return result, nil
}

const (
	// 收款类型
	FundTypeIncome = "0"
	// 退款类型
	FundTypeRefund = "1"

	PaymentMethodAssetPayment      = "0" // 资管支付
	PaymentMethodAssetWithhold     = "1" // 资管代扣
	PaymentMethodGuaranteePayment  = "2" // 担保支付
	PaymentMethodGuaranteeWithhold = "3" // 担保代扣
	PaymentMethodOfflineAlipay     = "4" // 支付宝支付（线下）
	PaymentMethodOfflineWechat     = "5" // 微信支付（线下）
	PaymentMethodOfflineBankCard   = "6" // 银行卡支付（线下）
	PaymentMethodOfflineCreditCard = "7" // 信用卡支付（线下）

	PaymentStatusEarly   = "0" // 提前收款
	PaymentStatusDue     = "1" // 到期收款
	PaymentStatusOverdue = "2" // 逾期收款
)

// buildIncomeDetailsQuery 构建收入明细查询条件
func (r *Repository) buildIncomeDetailsQuery(params map[string]interface{}) gform.IOrm {
	query := model.DB(model.WithContext(r.ctx)).Table("business_payment_transactions pt").
		Join("business_repayment_bills rb", "pt.bill_id = rb.id").
		Join("business_loan_orders lo", "pt.order_id = lo.id").
		LeftJoin("business_app_account aa", "pt.user_id = aa.id").
		Where("pt.status", model.TransactionStatusSuccess). // 处理成功
		Where("pt.deleted_at IS NULL")

	// 订单编号筛选
	if orderNo, ok := params["order_no"]; ok && orderNo != "" {
		query = query.Where("lo.order_no", "LIKE", "%"+orderNo.(string)+"%")
	}

	// 用户姓名筛选
	if userName, ok := params["user_name"]; ok && userName != "" {
		query = query.Where("aa.name", "LIKE", "%"+userName.(string)+"%")
	}

	// 手机号筛选
	if mobile, ok := params["mobile"]; ok && mobile != "" {
		query = query.Where("aa.mobile", "LIKE", "%"+mobile.(string)+"%")
	}

	// 款项类型筛选
	if fundType, ok := params["fund_type"]; ok && fundType != "" {
		switch fundType {
		case FundTypeIncome:
			query = query.WhereIn("pt.type", []interface{}{
				model.TransactionTypeRepayment,
				model.TransactionTypeWithhold,
				model.TransactionTypeManualWithhold,
				model.TransactionTypePartialOfflineRepayment,
			})
		case FundTypeRefund:
			query = query.Where("pt.type", model.TransactionTypeRefund)
		}
	} else {
		query = query.WhereIn("pt.type", []interface{}{
			model.TransactionTypeRepayment,
			model.TransactionTypeWithhold,
			model.TransactionTypeManualWithhold,
			model.TransactionTypePartialOfflineRepayment,
			model.TransactionTypeRefund,
		})
	}

	// 收款方式筛选
	if paymentMethod, ok := params["payment_method"]; ok && paymentMethod != "" {
		switch paymentMethod {
		case PaymentMethodAssetPayment:
			query = query.Where("pt.withhold_type", model.WithholdTypeAsset).Where("pt.type", model.TransactionTypeRepayment)
		case PaymentMethodAssetWithhold:
			query = query.Where("pt.withhold_type", model.WithholdTypeAsset).Where(func() {
				// 系统代扣或手动代扣
				query.Where("pt.type", model.TransactionTypeWithhold).OrWhere("pt.type", model.TransactionTypeManualWithhold)
			})
		case PaymentMethodGuaranteePayment:
			query = query.Where("pt.withhold_type", model.WithholdTypeGuarantee).Where("pt.type", model.TransactionTypeRepayment)
		case PaymentMethodGuaranteeWithhold:
			query = query.Where("pt.withhold_type", model.WithholdTypeGuarantee).Where(func() {
				// 系统代扣或手动代扣
				query.Where("pt.type", model.TransactionTypeWithhold).OrWhere("pt.type", model.TransactionTypeManualWithhold)
			})
		case PaymentMethodOfflineAlipay:
			query = query.Where("pt.offline_payment_channel_detail", payment.PaymentChannelAlipay) // 支付宝支付（线下）
		case PaymentMethodOfflineWechat:
			query = query.Where("pt.offline_payment_channel_detail", payment.PaymentChannelWechat) // 微信支付（线下）
		case PaymentMethodOfflineBankCard:
			query = query.Where("pt.offline_payment_channel_detail", payment.PaymentChannelBankCard) // 银行卡支付（线下）
		case PaymentMethodOfflineCreditCard:
			query = query.Where("pt.offline_payment_channel_detail", payment.PaymentChannelCreditCard) // 信用卡支付（线下）
		}
	}

	// 收款状态筛选
	if paymentStatus, ok := params["payment_status"]; ok && paymentStatus != "" {
		switch paymentStatus {
		case PaymentStatusEarly:
			query = query.Where("DATE(pt.completed_at)", "<", "DATE(rb.due_date)")
		case PaymentStatusDue:
			query = query.Where("DATE(pt.completed_at)", "=", "DATE(rb.due_date)")
		case PaymentStatusOverdue:
			query = query.Where("DATE(pt.completed_at)", ">", "DATE(rb.due_date)")
		}
	}

	// 收款时间范围筛选
	if startTime, ok := params["payment_time_start"]; ok && startTime != "" {
		query = query.Where("DATE(pt.completed_at)", ">=", startTime)
	}
	if endTime, ok := params["payment_time_end"]; ok && endTime != "" {
		query = query.Where("DATE(pt.completed_at)", "<=", endTime)
	}

	// 账单时间范围筛选
	if startTime, ok := params["bill_time_start"]; ok && startTime != "" {
		query = query.Where("rb.due_date", ">=", startTime)
	}
	if endTime, ok := params["bill_time_end"]; ok && endTime != "" {
		query = query.Where("rb.due_date", "<=", endTime)
	}

	return query
}

// ChannelDueStatistics 渠道到期统计相关的数据结构

// BasicBillStats 基础账单统计
type BasicBillStats struct {
	ChannelID       int     `db:"channel_id"`
	ChannelName     string  `db:"channel_name"`
	TotalBills      int     `db:"total_bills"`
	TotalDueAmount  float64 `db:"total_due_amount"`
	TotalPaidAmount float64 `db:"total_paid_amount"`
}

// UserTypeStats 新老用户分类统计
type UserTypeStats struct {
	ChannelID         int     `db:"channel_id"`
	NewUserBills      int     `db:"new_user_bills"`
	NewUserDueAmount  float64 `db:"new_user_due_amount"`
	NewUserPaidAmount float64 `db:"new_user_paid_amount"`
	OldUserBills      int     `db:"old_user_bills"`
	OldUserDueAmount  float64 `db:"old_user_due_amount"`
	OldUserPaidAmount float64 `db:"old_user_paid_amount"`
}

// RepaymentUserStats 还款人数统计
type RepaymentUserStats struct {
	ChannelID      int `db:"channel_id"`
	RepaymentUsers int `db:"repayment_users"`
}

// RepeatPurchaseStats 复购统计
type RepeatPurchaseStats struct {
	ChannelID            int `db:"channel_id"`
	RepeatPurchaseUsers  int `db:"repeat_purchase_users"`  // 还款复购人数：还款用户中订单数>1的用户
	TotalOrderUsers      int `db:"total_order_users"`      // 下单用户数：本渠道所有下单用户
	TotalRepurchaseUsers int `db:"total_repurchase_users"` // 复购用户数：下单用户中订单数>1的用户
}

// GetBasicBillStatistics 获取基础账单统计
func (r *Repository) GetBasicBillStatistics(params map[string]interface{}) ([]BasicBillStats, error) {
	baseQuery := `
		WITH user_order_counts AS (
			SELECT user_id, COUNT(*) as order_count
			FROM business_loan_orders
			GROUP BY user_id
		)
		SELECT
			c.id as channel_id,
			c.channel_name,
			COUNT(b.id) as total_bills,
			COALESCE(SUM(b.total_due_amount), 0) as total_due_amount,
			COALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount
		FROM channel c
		LEFT JOIN business_loan_orders o ON c.id = o.channel_id
		LEFT JOIN business_repayment_bills b ON o.id = b.order_id
		LEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id`

	// 使用通用筛选条件构建方法
	whereClause, args := r.buildChannelDueStatisticsWhereClause(params)

	query := baseQuery + " " + whereClause + " GROUP BY c.id, c.channel_name ORDER BY c.id"

	result, err := model.DB(model.WithContext(r.ctx)).Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询基础账单统计失败: %v", err)
	}

	var stats []BasicBillStats
	for _, row := range result {
		stat := BasicBillStats{
			ChannelID:       gconv.Int(row["channel_id"]),
			ChannelName:     gconv.String(row["channel_name"]),
			TotalBills:      gconv.Int(row["total_bills"]),
			TotalDueAmount:  gconv.Float64(row["total_due_amount"]),
			TotalPaidAmount: gconv.Float64(row["total_paid_amount"]),
		}
		stats = append(stats, stat)
	}

	return stats, nil
}

// GetUserTypeStatistics 获取新老用户分类统计
func (r *Repository) GetUserTypeStatistics(params map[string]interface{}) ([]UserTypeStats, error) {
	baseQuery := `
		WITH user_order_counts AS (
			SELECT user_id, COUNT(*) as order_count
			FROM business_loan_orders
			GROUP BY user_id
		)
		SELECT
			c.id as channel_id,
			COUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,
			COALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,
			COALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,
			COUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,
			COALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,
			COALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount
		FROM channel c
		LEFT JOIN business_loan_orders o ON c.id = o.channel_id
		LEFT JOIN business_repayment_bills b ON o.id = b.order_id
		LEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id`

	// 使用通用筛选条件构建方法
	whereClause, args := r.buildChannelDueStatisticsWhereClause(params)

	query := baseQuery + " " + whereClause + " GROUP BY c.id ORDER BY c.id"

	result, err := model.DB(model.WithContext(r.ctx)).Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询新老用户统计失败: %v", err)
	}

	var stats []UserTypeStats
	for _, row := range result {
		stat := UserTypeStats{
			ChannelID:         gconv.Int(row["channel_id"]),
			NewUserBills:      gconv.Int(row["new_user_bills"]),
			NewUserDueAmount:  gconv.Float64(row["new_user_due_amount"]),
			NewUserPaidAmount: gconv.Float64(row["new_user_paid_amount"]),
			OldUserBills:      gconv.Int(row["old_user_bills"]),
			OldUserDueAmount:  gconv.Float64(row["old_user_due_amount"]),
			OldUserPaidAmount: gconv.Float64(row["old_user_paid_amount"]),
		}
		stats = append(stats, stat)
	}

	return stats, nil
}

// GetRepaymentUserStatistics 获取还款人数统计
func (r *Repository) GetRepaymentUserStatistics(params map[string]interface{}) ([]RepaymentUserStats, error) {
	baseQuery := `
		WITH user_order_counts AS (
			SELECT user_id, COUNT(*) as order_count
			FROM business_loan_orders
			GROUP BY user_id
		)
		SELECT
			c.id as channel_id,
			COUNT(DISTINCT o.user_id) as repayment_users
		FROM channel c
		LEFT JOIN business_loan_orders o ON c.id = o.channel_id
		LEFT JOIN business_repayment_bills b ON o.id = b.order_id
		LEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id
		LEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id`

	// 使用通用筛选条件构建方法
	whereClause, args := r.buildChannelDueStatisticsWhereClause(params)

	// 添加还款相关的特定条件
	whereClause += " AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')"

	query := baseQuery + " " + whereClause + " GROUP BY c.id ORDER BY c.id"

	result, err := model.DB(model.WithContext(r.ctx)).Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询还款人数统计失败: %v", err)
	}

	var stats []RepaymentUserStats
	for _, row := range result {
		stat := RepaymentUserStats{
			ChannelID:      gconv.Int(row["channel_id"]),
			RepaymentUsers: gconv.Int(row["repayment_users"]),
		}
		stats = append(stats, stat)
	}

	return stats, nil
}

// GetRepeatPurchaseStatistics 获取复购统计
func (r *Repository) GetRepeatPurchaseStatistics(params map[string]interface{}) ([]RepeatPurchaseStats, error) {
	baseQuery := `
		WITH user_order_counts AS (
			SELECT user_id, COUNT(*) as order_count
			FROM business_loan_orders
			GROUP BY user_id
		),
		repayment_users AS (
			SELECT DISTINCT o.user_id, o.channel_id
			FROM business_loan_orders o
			INNER JOIN business_repayment_bills b ON o.id = b.order_id
			INNER JOIN business_payment_transactions pt ON b.id = pt.bill_id
			WHERE pt.status = 2
			AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')
		)
		SELECT
			c.id as channel_id,
			COUNT(DISTINCT CASE
				WHEN ru.user_id IS NOT NULL AND uoc.order_count > 1
				THEN o.user_id
			END) as repeat_purchase_users,
			COUNT(DISTINCT o.user_id) as total_order_users,
			COUNT(DISTINCT CASE
				WHEN uoc.order_count > 1
				THEN o.user_id
			END) as total_repurchase_users
		FROM channel c
		LEFT JOIN business_loan_orders o ON c.id = o.channel_id
		INNER JOIN business_repayment_bills b ON o.id = b.order_id
		INNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id
		LEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id`

	// 使用通用筛选条件构建方法
	whereClause, args := r.buildChannelDueStatisticsWhereClause(params)

	query := baseQuery + " " + whereClause + " GROUP BY c.id ORDER BY c.id"

	result, err := model.DB(model.WithContext(r.ctx)).Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询复购统计失败: %v", err)
	}

	var stats []RepeatPurchaseStats
	for _, row := range result {
		stat := RepeatPurchaseStats{
			ChannelID:            gconv.Int(row["channel_id"]),
			RepeatPurchaseUsers:  gconv.Int(row["repeat_purchase_users"]),
			TotalOrderUsers:      gconv.Int(row["total_order_users"]),
			TotalRepurchaseUsers: gconv.Int(row["total_repurchase_users"]),
		}
		stats = append(stats, stat)
	}

	return stats, nil
}

// GetChannelDueStatisticsList 获取渠道到期统计分页列表
func (r *Repository) GetChannelDueStatisticsList(params map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	// 构建渠道计数查询（统计所有启用渠道数量，包括没有订单的渠道）
	countBaseQuery := `
		WITH user_order_counts AS (
			SELECT user_id, COUNT(*) as order_count
			FROM business_loan_orders
			GROUP BY user_id
		)
		SELECT COUNT(DISTINCT c.id) as count
		FROM channel c
		LEFT JOIN business_loan_orders o ON c.id = o.channel_id
		LEFT JOIN business_repayment_bills b ON o.id = b.order_id
		LEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id`

	// 构建数据查询（显示所有启用渠道，包括没有订单的渠道）
	dataBaseQuery := `
		WITH user_order_counts AS (
			SELECT user_id, COUNT(*) as order_count
			FROM business_loan_orders
			GROUP BY user_id
		)
		SELECT DISTINCT c.id as channel_id, c.channel_name
		FROM channel c
		LEFT JOIN business_loan_orders o ON c.id = o.channel_id
		LEFT JOIN business_repayment_bills b ON o.id = b.order_id
		LEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id`

	// 使用渠道列表专用筛选条件构建方法
	whereClause, args := r.buildChannelDueStatisticsWhereClause(params)

	countQuery := countBaseQuery + " " + whereClause
	dataQuery := dataBaseQuery + " " + whereClause + " GROUP BY c.id, c.channel_name ORDER BY c.id"

	// 执行计数查询
	countResult, err := model.DB(model.WithContext(r.ctx)).Query(countQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("查询渠道数量失败: %v", err)
	}

	total := 0
	if len(countResult) > 0 {
		total = gconv.Int(countResult[0]["count"])
	}

	// 计算分页参数
	offset := (paginationReq.Page - 1) * paginationReq.PageSize
	dataQueryWithPagination := dataQuery + fmt.Sprintf(" LIMIT %d OFFSET %d", paginationReq.PageSize, offset)

	// 执行数据查询
	dataResult, err := model.DB(model.WithContext(r.ctx)).Query(dataQueryWithPagination, args...)
	if err != nil {
		return nil, fmt.Errorf("查询渠道列表失败: %v", err)
	}

	// 构建分页响应
	totalInt64 := int64(total)
	result := &pagination.PaginationResponse{
		Total:      totalInt64,
		Page:       paginationReq.Page,
		PageSize:   paginationReq.PageSize,
		TotalPages: (total + paginationReq.PageSize - 1) / paginationReq.PageSize,
		HasNext:    paginationReq.Page < (total+paginationReq.PageSize-1)/paginationReq.PageSize,
		HasPrev:    paginationReq.Page > 1,
		Data:       dataResult,
	}

	return result, nil
}

// buildChannelDueStatisticsWhereClause 构建渠道到期统计通用筛选条件
func (r *Repository) buildChannelDueStatisticsWhereClause(params map[string]interface{}) (string, []interface{}) {
	var whereClause strings.Builder
	var args []interface{}

	// 基础条件：渠道状态为启用
	whereClause.WriteString("WHERE c.channel_status = ?")
	args = append(args, model.ChannelStatusEnabled)

	// 时间范围筛选
	if dueDateStart, ok := params["due_date_start"]; ok && dueDateStart != "" {
		if dueDateEnd, ok := params["due_date_end"]; ok && dueDateEnd != "" {
			whereClause.WriteString(" AND b.due_date BETWEEN ? AND ?")
			args = append(args, dueDateStart, dueDateEnd)
		} else {
			whereClause.WriteString(" AND b.due_date >= ?")
			args = append(args, dueDateStart)
		}
	} else if dueDateEnd, ok := params["due_date_end"]; ok && dueDateEnd != "" {
		whereClause.WriteString(" AND b.due_date <= ?")
		args = append(args, dueDateEnd)
	}

	// 渠道筛选
	if channelID, ok := params["channel_id"]; ok && channelID != "" {
		whereClause.WriteString(" AND c.id = ?")
		args = append(args, channelID)
	}

	// 期数筛选
	if periodNumber, ok := params["period_number"]; ok && periodNumber != "" {
		whereClause.WriteString(" AND b.period_number = ?")
		args = append(args, periodNumber)
	}

	// 新老用户筛选
	if isNewUser, ok := params["is_new_user"]; ok && isNewUser != "" {
		if gconv.Int(isNewUser) == 1 {
			whereClause.WriteString(" AND uoc.order_count = 1")
		} else {
			whereClause.WriteString(" AND uoc.order_count > 1")
		}
	}

	return whereClause.String(), args
}

// GetExpenseDetailsStatistics 获取支出明细统计数据
func (r *Repository) GetExpenseDetailsStatistics(params map[string]interface{}) (*ExpenseStatistics, error) {
	// 构建基础查询
	baseQuery := model.DB(model.WithContext(r.ctx)).Table("business_payment_transactions bpt").
		LeftJoin("business_app_account baa", "bpt.user_id = baa.id").
		Join("business_loan_orders blo", "bpt.order_id = blo.id").
		Where("bpt.status", model.TransactionStatusSuccess).
		Where("bpt.type", model.TransactionTypeDisbursement).
		WhereNotNull("bpt.completed_at")

	// 应用筛选条件
	baseQuery = r.applyExpenseDetailsFilters(baseQuery, params)

	// 获取基础统计数据
	baseResult, err := baseQuery.Fields(
		"COALESCE(SUM(bpt.amount), 0) as total_expense",
		"COUNT(*) as expense_count",
		"COUNT(DISTINCT bpt.user_id) as expense_users",
		"COALESCE(SUM(CASE WHEN DATE(bpt.completed_at) = CURDATE() THEN bpt.amount ELSE 0 END), 0) as today_expense",
	).First()
	if err != nil {
		return nil, fmt.Errorf("查询基础统计数据失败: %v", err)
	}

	// 获取新老用户统计
	newUserExpense, oldUserExpense, err := r.getNewOldUserExpenseStats(params)
	if err != nil {
		return nil, fmt.Errorf("查询新老用户统计失败: %v", err)
	}

	statistics := &ExpenseStatistics{
		TotalExpense:   gconv.Float64(baseResult["total_expense"]),
		NewUserExpense: newUserExpense,
		OldUserExpense: oldUserExpense,
		TodayExpense:   gconv.Float64(baseResult["today_expense"]),
		ExpenseCount:   gconv.Int(baseResult["expense_count"]),
		ExpenseOrders:  gconv.Int(baseResult["expense_count"]), // 支出订单数同支出笔数
		ExpenseUsers:   gconv.Int(baseResult["expense_users"]),
	}

	return statistics, nil
}

// getNewOldUserExpenseStats 获取新老用户支出统计
func (r *Repository) getNewOldUserExpenseStats(params map[string]interface{}) (newUserExpense, oldUserExpense float64, err error) {
	// 构建查询，获取每个用户的订单数和支出金额
	query := model.DB(model.WithContext(r.ctx)).Table("business_payment_transactions bpt").
		LeftJoin("business_app_account baa", "bpt.user_id = baa.id").
		Join("business_loan_orders blo", "bpt.order_id = blo.id").
		Where("bpt.status", model.TransactionStatusSuccess).
		Where("bpt.type", model.TransactionTypeDisbursement).
		WhereNotNull("bpt.completed_at")

	// 应用筛选条件
	query = r.applyExpenseDetailsFilters(query, params)

	// 获取用户支出数据，包含用户订单数统计
	results, err := query.Fields(
		"bpt.user_id",
		"SUM(bpt.amount) as user_expense",
		// "(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = bpt.user_id AND sub.status IN (1,3) AND sub.disbursed_at IS NOT NULL) as user_order_count",
		"(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = bpt.user_id) as user_order_count",
	).GroupBy("bpt.user_id").Get()

	if err != nil {
		return 0, 0, fmt.Errorf("查询用户支出数据失败: %v", err)
	}

	// 计算新老用户支出
	for _, result := range results {
		userExpense := gconv.Float64(result["user_expense"])
		orderCount := gconv.Int(result["user_order_count"])

		if orderCount == 1 {
			// 使用shopspringutils进行精确金额计算
			newUserExpense = shopspringutils.AddAmountsWithDecimal(newUserExpense, userExpense)
		} else if orderCount > 1 {
			oldUserExpense = shopspringutils.AddAmountsWithDecimal(oldUserExpense, userExpense)
		}
	}

	return newUserExpense, oldUserExpense, nil
}

// GetExpenseDetailsList 获取支出明细列表数据
func (r *Repository) GetExpenseDetailsList(params map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	// 构建基础查询
	query := model.DB(model.WithContext(r.ctx)).Table("business_payment_transactions bpt").
		LeftJoin("business_app_account baa", "bpt.user_id = baa.id").
		Join("business_loan_orders blo", "bpt.order_id = blo.id").
		LeftJoin("contracts c", "blo.contract_id = c.id").
		LeftJoin("business_bank_cards bbc", "c.bank_card_id = bbc.id").
		Where("bpt.status", model.TransactionStatusSuccess).
		Where("bpt.type", model.TransactionTypeDisbursement).
		WhereNotNull("bpt.completed_at")

	// 应用筛选条件
	query = r.applyExpenseDetailsFilters(query, params)

	// 设置查询字段
	query = query.Fields(
		"blo.order_no",
		"bpt.amount",
		"baa.name as user_name",
		"baa.mobile",
		"bbc.bank_card_no",
		"DATE_FORMAT(bpt.completed_at, '%Y-%m-%d %H:%i:%s') AS completed_at",
		"bpt.user_id",
	).OrderBy("bpt.id DESC")

	// 执行分页查询
	return pagination.Paginate(query, paginationReq)
}

// applyExpenseDetailsFilters 应用支出明细筛选条件
func (r *Repository) applyExpenseDetailsFilters(query gform.IOrm, params map[string]interface{}) gform.IOrm {
	// 时间范围筛选
	if dateStart, ok := params["date_start"]; ok && dateStart != "" {
		dateStartStr := gconv.String(dateStart)
		dateStartTime := carbon.Parse(dateStartStr).StartOfDay().StdTime()
		if dateEnd, ok := params["date_end"]; ok && dateEnd != "" {
			dateEndTime := carbon.Parse(gconv.String(dateEnd)).EndOfDay().StdTime()
			query = query.WhereBetween("bpt.completed_at", []interface{}{dateStartTime, dateEndTime})
		} else {
			query = query.Where("bpt.completed_at", ">=", dateStartTime)
		}
	} else if dateEnd, ok := params["date_end"]; ok && dateEnd != "" {
		dateEndTime := carbon.Parse(gconv.String(dateEnd)).EndOfDay().StdTime()
		query = query.Where("bpt.completed_at", "<=", dateEndTime)
	}

	// 渠道筛选
	if channelID, ok := params["channel_id"]; ok && channelID != "" {
		query = query.Where("blo.channel_id", channelID)
	}

	// 用户姓名筛选
	if userName, ok := params["user_name"]; ok && userName != "" {
		query = query.Where("baa.name", "LIKE", "%"+gconv.String(userName)+"%")
	}

	// 手机号筛选
	if mobile, ok := params["mobile"]; ok && mobile != "" {
		query = query.Where("baa.mobile", "LIKE", "%"+gconv.String(mobile)+"%")
	}

	return query
}

// GetDueStatisticsData 获取到期统计数据
func (r *Repository) GetDueStatisticsData(params map[string]interface{}) (*DueStatisticsData, error) {
	dueDateStart := gconv.String(params["due_date_start"])
	dueDateEnd := gconv.String(params["due_date_end"])

	// 如果时间参数为空，设置默认值
	if dueDateStart == "" && dueDateEnd == "" {
		// 查询到期时间小于等于今天的数据
		dueDateEnd = carbon.Now().Format("2006-01-02")
	}

	// 构建WHERE条件
	whereCondition := ""
	var queryArgs []interface{}

	if dueDateStart != "" && dueDateEnd != "" {
		whereCondition = "AND b.due_date BETWEEN ? AND ?"
		queryArgs = append(queryArgs, dueDateStart, dueDateEnd)
	} else if dueDateEnd != "" {
		whereCondition = "AND b.due_date <= ?"
		queryArgs = append(queryArgs, dueDateEnd)
	} else if dueDateStart != "" {
		whereCondition = "AND b.due_date >= ?"
		queryArgs = append(queryArgs, dueDateStart)
	}

	// 构建用户订单数统计的CTE
	userOrderCountsCTE := `
		WITH user_order_counts AS (
			SELECT user_id, COUNT(*) as order_count
			FROM business_loan_orders
			GROUP BY user_id
		)`

	// 构建还款用户统计的CTE
	repaymentUsersCTE := `
		repayment_users AS (
			SELECT DISTINCT b.user_id, o.channel_id
			FROM business_repayment_bills b
			INNER JOIN business_loan_orders o ON b.order_id = o.id
			INNER JOIN business_payment_transactions t ON b.id = t.bill_id
			WHERE t.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')
			AND t.status = 1
			` + whereCondition + `
		)`

	// 构建主查询WHERE条件
	mainWhereCondition := ""
	var mainQueryArgs []interface{}

	if dueDateStart != "" && dueDateEnd != "" {
		mainWhereCondition = "WHERE b.due_date BETWEEN ? AND ?"
		mainQueryArgs = append(mainQueryArgs, dueDateStart, dueDateEnd)
	} else if dueDateEnd != "" {
		mainWhereCondition = "WHERE b.due_date <= ?"
		mainQueryArgs = append(mainQueryArgs, dueDateEnd)
	} else if dueDateStart != "" {
		mainWhereCondition = "WHERE b.due_date >= ?"
		mainQueryArgs = append(mainQueryArgs, dueDateStart)
	}

	// 基础统计查询
	basicStatsQuery := userOrderCountsCTE + `, ` + repaymentUsersCTE + `
		SELECT
			COUNT(b.id) as total_bills,
			COUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,
			COUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,
			COALESCE(SUM(b.total_due_amount), 0) as total_due_amount_raw,
			COALESCE(SUM(b.total_waive_amount), 0) as total_waive_amount,
			COALESCE(SUM(b.due_principal), 0) as total_principal,
			COALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount ELSE 0 END), 0) as new_user_due_amount_raw,
			COALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_waive_amount ELSE 0 END), 0) as new_user_waive_amount,
			COALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount ELSE 0 END), 0) as old_user_due_amount_raw,
			COALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_waive_amount ELSE 0 END), 0) as old_user_waive_amount,
			COALESCE(SUM(b.paid_amount), 0) as total_paid_amount,
			COALESCE(SUM(b.total_waive_amount), 0) as total_waive_amount_sum,
			COALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.paid_amount ELSE 0 END), 0) as new_user_paid_amount,
			COALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_waive_amount ELSE 0 END), 0) as new_user_waive_amount_sum,
			COALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.paid_amount ELSE 0 END), 0) as old_user_paid_amount,
			COALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_waive_amount ELSE 0 END), 0) as old_user_waive_amount_sum
		FROM business_repayment_bills b
		INNER JOIN business_loan_orders o ON b.order_id = o.id
		INNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id
		` + mainWhereCondition

	// 执行基础统计查询
	repaymentQueryArgs := append(queryArgs, mainQueryArgs...)
	basicResults, err := model.DB(model.WithContext(r.ctx)).Query(basicStatsQuery, repaymentQueryArgs...)
	if err != nil {
		return nil, fmt.Errorf("查询基础统计数据失败: %v", err)
	}
	if len(basicResults) == 0 {
		return nil, fmt.Errorf("基础统计数据为空")
	}
	basicResult := basicResults[0]

	// 还款人数统计查询
	repaymentUsersQuery := userOrderCountsCTE + `, ` + repaymentUsersCTE + `
		SELECT
			COUNT(DISTINCT ru.user_id) as repayment_users,
			COUNT(DISTINCT CASE WHEN uoc.order_count > 1 THEN ru.user_id END) as repayment_repurchase_users
		FROM repayment_users ru
		INNER JOIN user_order_counts uoc ON ru.user_id = uoc.user_id`

	repaymentResults, err := model.DB(model.WithContext(r.ctx)).Query(repaymentUsersQuery, queryArgs...)
	if err != nil {
		return nil, fmt.Errorf("查询还款人数统计失败: %v", err)
	}
	if len(repaymentResults) == 0 {
		return nil, fmt.Errorf("还款人数统计数据为空")
	}
	repaymentResult := repaymentResults[0]

	// 还款可复购人数统计查询
	eligibleUsersQuery := userOrderCountsCTE + `, ` + repaymentUsersCTE + `
		SELECT
			COUNT(DISTINCT ru.user_id) as repayment_eligible_users
		FROM repayment_users ru
		INNER JOIN user_order_counts uoc ON ru.user_id = uoc.user_id
		INNER JOIN business_app_account baa ON ru.user_id = baa.id
		WHERE baa.reminderQuota > 0`

	eligibleResults, err := model.DB(model.WithContext(r.ctx)).GetISession().Query(eligibleUsersQuery, queryArgs...)
	if err != nil {
		return nil, fmt.Errorf("查询还款可复购人数失败: %v", err)
	}
	if len(eligibleResults) == 0 {
		return nil, fmt.Errorf("还款可复购人数统计数据为空")
	}
	eligibleResult := eligibleResults[0]

	// 计算
	totalDueAmountRaw := gconv.Float64(basicResult["total_due_amount_raw"])
	totalWaiveAmount := gconv.Float64(basicResult["total_waive_amount"])
	totalDueAmount := shopspringutils.SubtractAmountsWithDecimal(totalDueAmountRaw, totalWaiveAmount)

	newUserDueAmountRaw := gconv.Float64(basicResult["new_user_due_amount_raw"])
	newUserWaiveAmount := gconv.Float64(basicResult["new_user_waive_amount"])
	newUserDueAmount := shopspringutils.SubtractAmountsWithDecimal(newUserDueAmountRaw, newUserWaiveAmount)

	oldUserDueAmountRaw := gconv.Float64(basicResult["old_user_due_amount_raw"])
	oldUserWaiveAmount := gconv.Float64(basicResult["old_user_waive_amount"])
	oldUserDueAmount := shopspringutils.SubtractAmountsWithDecimal(oldUserDueAmountRaw, oldUserWaiveAmount)

	totalPaidAmount := gconv.Float64(basicResult["total_paid_amount"])
	totalWaiveAmountSum := gconv.Float64(basicResult["total_waive_amount_sum"])
	totalRepaymentAmount := shopspringutils.AddAmountsWithDecimal(totalPaidAmount, totalWaiveAmountSum)

	newUserPaidAmount := gconv.Float64(basicResult["new_user_paid_amount"])
	newUserWaiveAmountSum := gconv.Float64(basicResult["new_user_waive_amount_sum"])
	newUserRepaymentAmount := shopspringutils.AddAmountsWithDecimal(newUserPaidAmount, newUserWaiveAmountSum)

	oldUserPaidAmount := gconv.Float64(basicResult["old_user_paid_amount"])
	oldUserWaiveAmountSum := gconv.Float64(basicResult["old_user_waive_amount_sum"])
	oldUserRepaymentAmount := shopspringutils.AddAmountsWithDecimal(oldUserPaidAmount, oldUserWaiveAmountSum)

	// 构建返回数据
	statistics := &DueStatisticsData{
		TotalBills:               gconv.Int(basicResult["total_bills"]),
		NewUserBills:             gconv.Int(basicResult["new_user_bills"]),
		OldUserBills:             gconv.Int(basicResult["old_user_bills"]),
		TotalDueAmount:           FormatAmount(totalDueAmount),
		TotalPrincipal:           FormatAmount(gconv.Float64(basicResult["total_principal"])),
		NewUserDueAmount:         FormatAmount(newUserDueAmount),
		OldUserDueAmount:         FormatAmount(oldUserDueAmount),
		TotalRepaymentAmount:     FormatAmount(totalRepaymentAmount),
		RepaymentUsers:           gconv.Int(repaymentResult["repayment_users"]),
		RepaymentRepurchaseUsers: gconv.Int(repaymentResult["repayment_repurchase_users"]),
		RepaymentEligibleUsers:   gconv.Int(eligibleResult["repayment_eligible_users"]),
	}

	// 使用shopspringutils计算回款率
	if shopspringutils.CompareAmountsWithDecimal(totalDueAmount, 0) > 0 {
		rate := shopspringutils.DivideAmountsWithDecimal(totalRepaymentAmount, totalDueAmount)
		statistics.TotalRepaymentRate = fmt.Sprintf("%.2f", shopspringutils.CeilToTwoDecimal(shopspringutils.MultiplyAmountsWithDecimal(rate, 100)))
	} else {
		statistics.TotalRepaymentRate = "0.00"
	}

	if shopspringutils.CompareAmountsWithDecimal(newUserDueAmount, 0) > 0 {
		rate := shopspringutils.DivideAmountsWithDecimal(newUserRepaymentAmount, newUserDueAmount)
		statistics.NewUserRepaymentRate = fmt.Sprintf("%.2f", shopspringutils.CeilToTwoDecimal(shopspringutils.MultiplyAmountsWithDecimal(rate, 100)))
	} else {
		statistics.NewUserRepaymentRate = "0.00"
	}

	if shopspringutils.CompareAmountsWithDecimal(oldUserDueAmount, 0) > 0 {
		rate := shopspringutils.DivideAmountsWithDecimal(oldUserRepaymentAmount, oldUserDueAmount)
		statistics.OldUserRepaymentRate = fmt.Sprintf("%.2f", shopspringutils.CeilToTwoDecimal(shopspringutils.MultiplyAmountsWithDecimal(rate, 100)))
	} else {
		statistics.OldUserRepaymentRate = "0.00"
	}

	// 计算复购率
	if statistics.RepaymentEligibleUsers > 0 {
		rate := shopspringutils.DivideAmountsWithDecimal(float64(statistics.RepaymentRepurchaseUsers), float64(statistics.RepaymentEligibleUsers))
		statistics.RepurchaseRate = fmt.Sprintf("%.2f", shopspringutils.CeilToTwoDecimal(shopspringutils.MultiplyAmountsWithDecimal(rate, 100)))
	} else {
		statistics.RepurchaseRate = "0.00"
	}

	return statistics, nil
}

// GetDueStatisticsList 获取到期统计列表数据
func (r *Repository) GetDueStatisticsList(params map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	dueDateStart := gconv.String(params["due_date_start"])
	dueDateEnd := gconv.String(params["due_date_end"])

	// 如果时间参数为空，设置默认值
	if dueDateStart == "" && dueDateEnd == "" {
		// 查询到期时间小于等于今天的数据
		dueDateEnd = carbon.Now().Format("2006-01-02")
	}

	// 构建用户订单数统计的CTE
	userOrderCountsCTE := `
		WITH user_order_counts AS (
			SELECT user_id, COUNT(*) as order_count
			FROM business_loan_orders
			GROUP BY user_id
		)`

	// 构建WHERE条件
	whereCondition := ""
	var queryArgs []interface{}

	if dueDateStart != "" && dueDateEnd != "" {
		whereCondition = "WHERE b.due_date BETWEEN ? AND ?"
		queryArgs = append(queryArgs, dueDateStart, dueDateEnd)
	} else if dueDateEnd != "" {
		whereCondition = "WHERE b.due_date <= ?"
		queryArgs = append(queryArgs, dueDateEnd)
	} else if dueDateStart != "" {
		whereCondition = "WHERE b.due_date >= ?"
		queryArgs = append(queryArgs, dueDateStart)
	}

	// 构建还款流水的WHERE条件
	repaymentWhereCondition := ""
	if dueDateStart != "" && dueDateEnd != "" {
		repaymentWhereCondition = "AND b.due_date BETWEEN ? AND ?"
	} else if dueDateEnd != "" {
		repaymentWhereCondition = "AND b.due_date <= ?"
	} else if dueDateStart != "" {
		repaymentWhereCondition = "AND b.due_date >= ?"
	}

	// 构建还款用户统计的CTE
	repaymentUsersCTE := `
		repayment_users AS (
			SELECT DISTINCT b.user_id, o.channel_id, b.due_date
			FROM business_repayment_bills b
			INNER JOIN business_loan_orders o ON b.order_id = o.id
			INNER JOIN business_payment_transactions t ON b.id = t.bill_id
			WHERE t.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')
			AND t.status = 1
			` + repaymentWhereCondition + `
		)`

	// 当日还款用户统计的CTE
	todayRepaymentCTE := `
		today_repayment AS (
			SELECT b.due_date, SUM(t.amount) as today_amount
			FROM business_repayment_bills b
			INNER JOIN business_payment_transactions t ON b.id = t.bill_id
			WHERE t.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')
			AND t.status = 1
			AND DATE(t.completed_at) = b.due_date
			` + repaymentWhereCondition + `
			GROUP BY b.due_date
		)`

	// 构建列表查询SQL - 不在SQL中进行复杂计算
	listQuery := userOrderCountsCTE + `, ` + repaymentUsersCTE + `, ` + todayRepaymentCTE + `
		SELECT
			b.due_date,
			COUNT(b.id) as total_bills,
			COUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,
			COUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,
			COALESCE(SUM(b.total_due_amount), 0) as total_due_amount_raw,
			COALESCE(SUM(b.total_waive_amount), 0) as total_waive_amount,
			COALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount ELSE 0 END), 0) as new_user_due_amount_raw,
			COALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_waive_amount ELSE 0 END), 0) as new_user_waive_amount,
			COALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount ELSE 0 END), 0) as old_user_due_amount_raw,
			COALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_waive_amount ELSE 0 END), 0) as old_user_waive_amount,
			COALESCE(SUM(b.paid_amount), 0) as paid_amount,
			COALESCE(SUM(b.total_waive_amount), 0) as waive_amount_sum,
			COALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.paid_amount ELSE 0 END), 0) as new_user_paid_amount,
			COALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_waive_amount ELSE 0 END), 0) as new_user_waive_amount_sum,
			COALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.paid_amount ELSE 0 END), 0) as old_user_paid_amount,
			COALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_waive_amount ELSE 0 END), 0) as old_user_waive_amount_sum,
			COALESCE(tr.today_amount, 0) as today_repayment_amount,
			COUNT(DISTINCT ru.user_id) as repayment_users,
			COUNT(DISTINCT CASE WHEN baa.reminderQuota > 0 THEN ru.user_id END) as repayment_eligible_users,
			COUNT(DISTINCT CASE WHEN uoc.order_count > 1 AND baa.reminderQuota > 0 THEN ru.user_id END) as repayment_repurchase_users
		FROM business_repayment_bills b
		INNER JOIN business_loan_orders o ON b.order_id = o.id
		INNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id
		LEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND b.due_date = ru.due_date
		LEFT JOIN business_app_account baa ON o.user_id = baa.id
		LEFT JOIN today_repayment tr ON b.due_date = tr.due_date
		` + whereCondition + `
		GROUP BY b.due_date
		ORDER BY b.due_date DESC`

	// 构建计数查询SQL
	countQuery := `
		SELECT COUNT(DISTINCT b.due_date) as total
		FROM business_repayment_bills b
		` + whereCondition

	// 执行计数查询
	countResults, err := model.DB(model.WithContext(r.ctx)).Query(countQuery, queryArgs...)
	if err != nil {
		return nil, fmt.Errorf("查询总数失败: %v", err)
	}

	total := int64(0)
	if len(countResults) > 0 {
		total = gconv.Int64(countResults[0]["total"])
	}

	// 计算分页参数
	offset := (paginationReq.Page - 1) * paginationReq.PageSize
	limitQuery := listQuery + fmt.Sprintf(" LIMIT %d OFFSET %d", paginationReq.PageSize, offset)

	// 执行列表查询 - 需要为每个CTE提供参数
	listQueryArgs := append(queryArgs, queryArgs...)
	listQueryArgs = append(listQueryArgs, queryArgs...)
	listResults, err := model.DB(model.WithContext(r.ctx)).Query(limitQuery, listQueryArgs...)
	if err != nil {
		return nil, fmt.Errorf("查询列表数据失败: %v", err)
	}

	// 构建分页响应
	response := &pagination.PaginationResponse{
		Total:      total,
		Page:       paginationReq.Page,
		PageSize:   paginationReq.PageSize,
		TotalPages: int((total + int64(paginationReq.PageSize) - 1) / int64(paginationReq.PageSize)),
		HasNext:    paginationReq.Page < int((total+int64(paginationReq.PageSize)-1)/int64(paginationReq.PageSize)),
		HasPrev:    paginationReq.Page > 1,
		Data:       listResults,
	}

	return response, nil
}
