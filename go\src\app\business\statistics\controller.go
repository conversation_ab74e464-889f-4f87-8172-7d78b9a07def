package statistics

import (
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"
	"reflect"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/v2/util/gconv"
)

// StatisticsController 统计控制器
type StatisticsController struct{}

func init() {
	controller := StatisticsController{}
	gf.Register(&controller, reflect.TypeOf(controller).PkgPath())
}

// GetChannelStatistics 获取渠道统计列表
func (c *StatisticsController) GetChannelStatistics(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetChannelStatisticsSchema()
	validator := jsonschema.NewValidator(schema)

	// 从查询参数获取数据
	queryData := map[string]interface{}{
		"date":      ctx.Query("date"),
		"page":      ctx.Query("page"),
		"page_size": ctx.Query("page_size"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range queryData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}
	}

	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := NewService(ctx)
	result, err := service.GetChannelStatisticsList(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取渠道统计列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取渠道统计列表成功", result, nil)
}

// GetHomeStatistics 首页数据统计
func (c *StatisticsController) GetHomeStatistics(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetHomeStatistics()
	validator := jsonschema.NewValidator(schema)

	// 从查询参数获取数据
	queryData := map[string]interface{}{
		"date_begin": ctx.Query("date_begin"),
		"date_end":   ctx.Query("date_end"),
	}

	validationResult := validator.Validate(queryData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	var dateBegin, dateEnd time.Time
	var err error
	// 开始时间不能大于结束时间
	if validationResult.Data["date_begin"] != "" && validationResult.Data["date_end"] != "" {
		dateBegin, err = time.Parse(time.DateOnly, validationResult.Data["date_begin"].(string))
		if err != nil {
			results.Failed(ctx, "开始时间格式错误", nil)
			return
		}
		dateEnd, err = time.Parse(time.DateOnly, validationResult.Data["date_end"].(string))
		if err != nil {
			results.Failed(ctx, "开始时间不能大于结束时间", nil)
			return
		}
		if dateBegin.After(dateEnd) {
			results.Failed(ctx, "开始时间不能大于结束时间", nil)
			return
		}
	}

	// 2. 业务逻辑处理
	service := NewService(ctx)
	resp, err := service.GetHomeStatistics(dateBegin, dateEnd)
	if err != nil {
		results.Failed(ctx, "获取首页数据统计失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取首页数据统计成功", resp, nil)
}

// GetTrendStatistics 首页统计趋势数据
func (c *StatisticsController) GetTrendStatistics(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetTrendStatisticsSchema()
	validator := jsonschema.NewValidator(schema)

	// 从查询参数获取数据
	queryData := map[string]interface{}{
		"days": ctx.Query("days"),
	}

	validationResult := validator.Validate(queryData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := NewService(ctx)
	days := gconv.Int(validationResult.Data["days"])
	resp, err := service.GetTrendStatistics(days)
	if err != nil {
		results.Failed(ctx, "获取趋势统计数据失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取趋势统计数据成功", resp, nil)
}

// GetIncomeDetails 获取收入明细
func (c *StatisticsController) GetIncomeDetails(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetIncomeDetailsSchema()
	validator := jsonschema.NewValidator(schema)

	// 从查询参数获取数据
	queryData := map[string]interface{}{
		"order_no":           ctx.Query("order_no"),
		"user_name":          ctx.Query("user_name"),
		"mobile":             ctx.Query("mobile"),
		"fund_type":          ctx.Query("fund_type"),
		"payment_method":     ctx.Query("payment_method"),
		"payment_status":     ctx.Query("payment_status"),
		"payment_time_start": ctx.Query("payment_time_start"),
		"payment_time_end":   ctx.Query("payment_time_end"),
		"bill_time_start":    ctx.Query("bill_time_start"),
		"bill_time_end":      ctx.Query("bill_time_end"),
		"page":               ctx.Query("page"),
		"page_size":          ctx.Query("page_size"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range queryData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}
	}

	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := NewService(ctx)
	result, err := service.GetIncomeDetailsList(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取收入明细失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取收入明细成功", result, nil)
}

// GetChannelDueStatistics 获取渠道到期统计
func (c *StatisticsController) GetChannelDueStatistics(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetChannelDueStatisticsSchema()
	validator := jsonschema.NewValidator(schema)

	// 从查询参数获取数据
	queryData := map[string]interface{}{
		"channel_id":     ctx.Query("channel_id"),
		"due_date_start": ctx.Query("due_date_start"),
		"due_date_end":   ctx.Query("due_date_end"),
		"period_number":  ctx.Query("period_number"),
		"is_new_user":    ctx.Query("is_new_user"),
		"page":           ctx.Query("page"),
		"page_size":      ctx.Query("page_size"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range queryData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}
	}

	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := NewService(ctx)
	result, err := service.GetChannelDueStatistics(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取渠道到期统计失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取渠道到期统计成功", result, nil)
}

// GetExpenseDetails 获取支出明细
func (c *StatisticsController) GetExpenseDetails(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetExpenseDetailsSchema()
	validator := jsonschema.NewValidator(schema)

	// 从查询参数获取数据
	queryData := map[string]interface{}{
		"date_start": ctx.Query("date_start"),
		"date_end":   ctx.Query("date_end"),
		"channel_id": ctx.Query("channel_id"),
		"user_name":  ctx.Query("user_name"),
		"mobile":     ctx.Query("mobile"),
		"page":       ctx.Query("page"),
		"page_size":  ctx.Query("page_size"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range queryData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}
	}

	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := NewService(ctx)
	result, err := service.GetExpenseDetailsList(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取支出明细失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取支出明细成功", result, nil)
}

// GetDueStatistics 获取到期统计
func (c *StatisticsController) GetDueStatistics(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetDueStatisticsSchema()
	validator := jsonschema.NewValidator(schema)

	// 从查询参数获取数据
	queryData := map[string]interface{}{
		"due_date_start": ctx.Query("due_date_start"),
		"due_date_end":   ctx.Query("due_date_end"),
		"page":           ctx.Query("page"),
		"page_size":      ctx.Query("page_size"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range queryData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}
	}

	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := NewService(ctx)
	result, err := service.GetDueStatistics(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取到期统计失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取到期统计成功", result, nil)
}
