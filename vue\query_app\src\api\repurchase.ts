import { defHttp } from '@/utils/http';

// 基础API响应接口
export interface BaseResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 复购客户查询参数接口
export interface RepurchaseCustomerQueryParams {
  // 基础信息
  name?: string;
  mobile?: string;
  idCard?: string;
  channelId?: string;
  
  // 状态相关
  isCancelled?: string;
  
  // 订单统计
  borrowingOrderCount?: string;
  totalOrderCount?: string;
  
  // 额度范围
  totalAmountMin?: string;
  totalAmountMax?: string;
  availableAmountMin?: string;
  availableAmountMax?: string;
  
  // 时间范围
  lastRepayTimeStart?: string;
  lastRepayTimeEnd?: string;
  billDueTimeStart?: string;
  billDueTimeEnd?: string;
  
  // 排序
  sortType?: string;
  
  // 分页
  page?: number;
  pageSize?: number;
}

// 复购客户信息接口
export interface RepurchaseCustomerItem {
  id: number;
  channelName: string;
  isCancelled: number;
  isCancelledText: string;
  name: string;
  mobile: string;
  idCardMasked: string;
  totalAmount: number;
  availableAmount: number;
  borrowingOrderCount: number;
  totalOrderCount: number;
  registerTime: string;
  lastRepayTime: string;
  billDueTime: string;
  lastAwakenTime: string;
  awakenCount: number;
  awakenRemark: string;
}

// 复购客户列表响应接口
export interface RepurchaseCustomerListResponse {
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  list: RepurchaseCustomerItem[];
}

// 筛选选项接口
export interface RepurchaseCustomerOptions {
  channelOptions: OptionItem[];
}

// 后端返回的原始唤醒记录接口
export interface RepurchaseAwakenRecordRaw {
  id: number;
  customerId: number;
  awakenContent: string;
  awakenType: number;
  awakenStatus: number;
  operatorId: number;
  operatorName: string;
  remark: string;
  createTime: string;
}

// 前端处理后的唤醒记录接口
export interface RepurchaseAwakenRecord {
  id: number;
  customerId: number;
  awakenContent: string;
  awakenType: number;
  awakenTypeText: string;
  awakenStatus: number;
  operatorId: number;
  operatorName: string;
  remark: string;
  recordTime: string;
  createTime: string;
}

// 发送短信参数接口
export interface SendRepurchaseSMSParams {
  customerIds: number[];
  templateType: string;
  remark?: string;
}

// 记录唤醒参数接口
export interface RecordRepurchaseAwakenParams {
  customerId: number;
  awakenContent: string;
  awakenType: number;
  remark: string;
}

export interface OptionItem {
  value: string;
  label: string;
}

enum Api {
  ListRepurchaseCustomers = '/customer/customercontroller/listRepurchaseCustomers',
  SendRepurchaseSMS = '/customer/customercontroller/sendRepurchaseSMS',
  RecordRepurchaseAwaken = '/customer/customercontroller/recordRepurchaseAwaken',
  GetRepurchaseAwakenRecords = '/customer/customercontroller/getRepurchaseAwakenRecords',
  GetRepurchaseCustomerOptions = '/customer/customercontroller/getRepurchaseCustomerOptions',
}

/**
 * 获取复购客户列表
 */
export function getRepurchaseCustomerList(params?: RepurchaseCustomerQueryParams) {
  return defHttp.post<RepurchaseCustomerListResponse>({
    url: Api.ListRepurchaseCustomers,
    params
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 发送复购短信
 */
export function sendRepurchaseSMS(params: SendRepurchaseSMSParams) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.SendRepurchaseSMS,
    params
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 记录复购唤醒
 */
export function recordRepurchaseAwaken(params: RecordRepurchaseAwakenParams) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.RecordRepurchaseAwaken,
    params
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 获取复购唤醒记录
 */
export function getRepurchaseAwakenRecords(customerId: number, page = 1, pageSize = 20) {
  return defHttp.get<RepurchaseAwakenRecordRaw[]>({
    url: Api.GetRepurchaseAwakenRecords,
    params: { customerId, page, pageSize }
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 获取复购客户筛选选项
 */
export function getRepurchaseCustomerOptions() {
  return defHttp.get<RepurchaseCustomerOptions>({
    url: Api.GetRepurchaseCustomerOptions
  }, {
    errorMessageMode: 'message'
  });
} 