.data-video {
  margin: 0 auto;
  position: relative;
  cursor: pointer;
}
  .data-video-content {
    position: relative;
    height: 420px;
    background: #f7f7f7;
  }
  .data-video-content video {
    width: 100%;
    height: 100%;
    outline: none;
    position: relative;
    z-index: 1;
    background: #000;
  }
  .data-video-uploading,
  .data-video-uploaded,
  .data-video-error {
    border: 1px solid #e6e6e6;
    background: #f6f6f6;
  }
  .data-video-content .data-video-mask {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
  }
  .data-video-done {
    height: auto;
    border: none;
    background: none;
    line-height: 0;
  }
  .data-video-active {
    user-select: none;
  }
  .data-video-center {
    position: absolute;
    top: 50%;
    margin-top: -48px;
    width: 100%;
    height: 96px;
  }
  .data-video-center .data-video-icon,
  .data-video-center .data-video-name,
  .data-video-center .data-video-message,
  .data-video-center .data-video-progress,
  .data-video-center .data-video-transcoding {
    text-align: center;
  }
  .data-video-center .data-video-icon {
    font-size: 24px;
    color: #BFBFBF;
    margin-bottom: 12px;
  }
  .data-video-center .data-video-name {
    color: #595959;
    margin-bottom: 12px;
  }
  .data-video-center .data-video-message {
    color: #595959;
  }
  .data-video-center .data-video-anticon {
    display: inline-block;
    font-style: normal;
    vertical-align: -0.125em;
    text-align: center;
    text-transform: none;
    line-height: 0;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    margin-right: 5px;
  }
  .data-video-center .data-video-anticon .data-video-anticon-spin {
    display: inline-block;
    -webkit-animation: loadingCircle 1s infinite linear;
    animation: loadingCircle 1s infinite linear;
  }
  .data-video-center .data-error-icon {
    width: 16px;
    height: 16px;
    display: inline-block;
    background: #F5222D;
    text-align: center;
    font-size: 12px;
    color: #ffffff;
    padding: 1px 0 0 0;
    line-height: 16px;
    border-radius: 100%;
    vertical-align: middle;
    margin: -2px 5px 0 0;
  }

.data-video-title {
  text-align: center;
}
.data-video-title::selection {
  background: transparent
}
.data-video .data-resizer {
  z-index: inherit;
}
.data-video .data-resizer .data-resizer-holder {
  z-index: 2;
}

.data-video .data-resizing {
  z-index: 3;
}