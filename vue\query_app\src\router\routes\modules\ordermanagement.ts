import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const ORDERMANAGEMENT: AppRouteRecordRaw = {
  path: '/ordermanagement',
  name: 'ordermanagement',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.ordermanagement',
    requiresAuth: true,
    icon: 'icon-file',
    order: 4,
  },
  children: [
    {
      path: 'Orderlist',
      name: 'Orderlist',
      component: () => import('@/views/ordermanagement/Orderlist/index.vue'),
      meta: {
        locale: 'menu.ordermanagement.Orderlist',
        requiresAuth: true,
        roles: ['*'],
        keepAlive: true,
      },
    },
    {
      path: 'Detail/:id',
      name: 'OrderDetail',
      component: () => import('@/views/ordermanagement/Detail/index.vue'),
      meta: {
        locale: 'menu.ordermanagement.detail',
        requiresAuth: true,
        roles: ['*'],
        hideInMenu: true,
      },
    },
  ],
};

export default ORDERMANAGEMENT; 