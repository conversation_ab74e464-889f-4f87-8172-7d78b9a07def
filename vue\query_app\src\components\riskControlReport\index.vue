<!-- 展示风控 -->
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { object } from 'vue-types';

const props = defineProps<{
  fkTabList: Array<any>
}>()

// 风控相关
const fkTabProps = {
  label: 'label',
  value: 'value',
  disabled: 'disabled',
};
const fkTabOptions = ref<Array<{label: string, value: any}>>([]);
const fkTabItem = ref({});
const fkTabValue = ref();
const zwscTxt = ref("-");
onMounted(() => {
  let reportsData = props.fkTabList;
  fkTabOptions.value = [];
  fkTabValue.value = '';
  reportsData.forEach((item: any, index: any) => {
    let txt = item.evaluation_id.indexOf('_async') != -1 ? '内部' : '外部';
    let time: string = item.evaluation_time.split(' ')[0] + ' ' + txt;
    fkTabOptions.value.push({ label: time, value: index });
  });
  fkTabValue.value = 0;
  fkTabItem.value = reportsData[0];
  zwscTransition(fkTabItem.value.raw_data.zwsc)
})
function handleFkSegmentedChange(e: number) {
  // console.log(e)
  fkTabItem.value = props.fkTabList[e];
  zwscTransition(fkTabItem.value.raw_data.zwsc)
}
// 在网时长转换
// (0，3)，表示三个月内
// (3，6)，表示三个月以上半年以内
// (6，12)，表示半年以上一年以内
// (12，24)，表示1-2年
// (24，null), 表示两年以上
function zwscTransition (zwscObj:any) {
  // min 和 max 都存在 走 getTimeRangeTextByMin 方法
  if(zwscObj && zwscObj.min && !zwscObj.max) {
    zwscTxt.value = getTimeRangeTextByMin(zwscObj.min)
  }
  // min存在 max不存在 走 getTimeRangeText 方法
  if(zwscObj && zwscObj.min && zwscObj.max) {
    zwscTxt.value = getTimeRangeText(zwscObj.min, zwscObj.max)
  }
}
function getTimeRangeText(min: number, max: number) {
  if (min === 0 && max === 3) {
    return "三个月内";
  } else if (min === 3 && max === 6) {
    return "三个月以上半年以内";
  } else if (min === 6 && max === 12) {
    return "半年以上一年以内";
  } else if (min === 12 && max === 24) {
    return "1-2年";
  } else if (min === 24 && max === null) {
    return "两年以上";
  } else {
    // 如果不在预定义的区间内，可以返回默认值或根据具体逻辑处理
    return "未知区间";
  }
}
function getTimeRangeTextByMin(min: number) {
  if (min >= 0 && min < 3) {
    return "三个月内";
  } else if (min >= 3 && min < 6) {
    return "三个月以上半年以内";
  } else if (min >= 6 && min < 12) {
    return "半年以上一年以内";
  } else if (min >= 12 && min < 24) {
    return "1-2年";
  } else if (min >= 24) {
    return "两年以上";
  } else {
    return "未知区间";
  }
}

function valueToTxt(obj:object, tKey: any, val: any) {
  if(obj && obj[tKey] && obj[tKey][val]) {
    return obj[tKey][val];
  }else {
    return "-";
  }
}
function scoreValueToTxt(obj:object, tKey: any, sKey:any, val: any) {
  if(obj && obj[tKey] && obj[tKey][sKey] && obj[tKey][sKey][val]) {
    return obj[tKey][sKey][val];
  }else {
    return "-";
  }
}
</script>

<template>
  <div class="fk-tab-title">
    <el-segmented
      v-model="fkTabValue"
      :options="fkTabOptions"
      :props="fkTabProps"
      @change="handleFkSegmentedChange"
    />
  </div>

  <el-descriptions border :column="3" label-width="120" v-if="Object.keys(fkTabItem).length > 0">
    <el-descriptions-item label="风控评分">
      {{ fkTabItem.risk_score }}
    </el-descriptions-item>
    <el-descriptions-item label="手机在网时长">{{ zwscTxt }}</el-descriptions-item>
    <el-descriptions-item label="风控结果">
      <el-tag :type="fkTabItem.risk_result == 0? 'primary': fkTabItem.risk_result == 2? 'danger': 'warning'">{{fkTabItem.risk_result == 0? '通过': fkTabItem.risk_result == 1? '人工审核': fkTabItem.risk_result == 2? '拒绝': '风控模型失败'}}</el-tag>
    </el-descriptions-item>
    <template v-if="fkTabItem.risk_result == 2">
      <el-descriptions-item label="风控类型">{{ fkTabItem.failure_type || '-' }}</el-descriptions-item>
      <el-descriptions-item label="风控拒绝原因">{{ fkTabItem.failure_reason || '-' }}</el-descriptions-item>
    </template>
  </el-descriptions>
  <el-divider>风险探针</el-divider>
  <el-descriptions
    border
    :column="4"
    label-width="130"
  >
    <el-descriptions-item label="最大逾期金额">{{
        valueToTxt(fkTabItem.raw_data, 'tan_zhen_c', 'max_overdue_amt')
      }}</el-descriptions-item>
    <el-descriptions-item label="最长逾期天数">{{
        valueToTxt(fkTabItem.raw_data, 'tan_zhen_c', 'max_overdue_days')
      }}</el-descriptions-item>
    <el-descriptions-item label="最近逾期时间">{{
        valueToTxt(fkTabItem.raw_data, 'tan_zhen_c', 'latest_overdue_time')
      }}</el-descriptions-item>
    <el-descriptions-item label="最大履约金额">{{
        valueToTxt(fkTabItem.raw_data, 'tan_zhen_c', 'max_performance_amt')
      }}</el-descriptions-item>
    <el-descriptions-item label="最近履约时间">{{
        valueToTxt(fkTabItem.raw_data, 'tan_zhen_c', 'latest_performance_time')
      }}</el-descriptions-item>
    <el-descriptions-item label="履约笔数">{{
        valueToTxt(fkTabItem.raw_data, 'tan_zhen_c', 'count_performance')
      }}</el-descriptions-item>
    <el-descriptions-item label="当前逾期机构数">{{
        valueToTxt(fkTabItem.raw_data, 'tan_zhen_c', 'currently_overdue')
      }}</el-descriptions-item>
    <el-descriptions-item label="当前履约机构数">{{
        valueToTxt(fkTabItem.raw_data, 'tan_zhen_c', 'currently_performance')
      }}</el-descriptions-item>
    <el-descriptions-item label="异常还款机构数">{{
        valueToTxt(fkTabItem.raw_data, 'tan_zhen_c', 'acc_exc')
      }}</el-descriptions-item>
    <el-descriptions-item label="睡眠机构数">{{
        valueToTxt(fkTabItem.raw_data, 'tan_zhen_c', 'acc_sleep')
      }}</el-descriptions-item>
  </el-descriptions>
  <el-divider>申请雷达</el-divider>
  <el-descriptions
    border
    :column="3"
    label-width="180"
  >
    <el-descriptions-item label="申请准入分">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'apply_report_detail', 'A22160001')
      }}</el-descriptions-item>
    <el-descriptions-item label="申请准入置信度">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'apply_report_detail', 'A22160002')
      }}</el-descriptions-item>
    <el-descriptions-item label="申请命中机构数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'apply_report_detail', 'A22160003')
      }}</el-descriptions-item>
    <el-descriptions-item label="申请命中消金类机构数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'apply_report_detail', 'A22160004')
      }}</el-descriptions-item>
    <el-descriptions-item label="申请命中网络贷款类机构数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'apply_report_detail', 'A22160005')
      }}</el-descriptions-item>
    <el-descriptions-item label="机构总查询次数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'apply_report_detail', 'A22160006')
      }}</el-descriptions-item>
    <el-descriptions-item label="最近一次查询时间">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'apply_report_detail', 'A22160007')
      }}</el-descriptions-item>
    <el-descriptions-item label="近1个月机构总查询笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'apply_report_detail', 'A22160008')
      }}</el-descriptions-item>
    <el-descriptions-item label="近3个月机构总查询笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'apply_report_detail', 'A22160009')
      }}</el-descriptions-item>
    <el-descriptions-item label="近6个月机构总查询笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'apply_report_detail', 'A22160010')
      }}</el-descriptions-item>
  </el-descriptions>
  <el-divider>还款行为</el-divider>
  <el-descriptions
    border
    :column="3"
    label-width="220"
  >
    <el-descriptions-item label="贷款行为分">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170001')
      }}</el-descriptions-item>
    <el-descriptions-item label="贷款行为置信度">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170051')
      }}</el-descriptions-item>
    <el-descriptions-item label="正常还款订单数占贷款总订单数比例">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170034')
      }}</el-descriptions-item>
    <el-descriptions-item label="近1个月贷款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170002')
      }}</el-descriptions-item>
    <el-descriptions-item label="近3个月贷款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170003')
      }}</el-descriptions-item>
    <el-descriptions-item label="近6个月贷款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170004')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月贷款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170005')
      }}</el-descriptions-item>
    <el-descriptions-item span="2" label="近24个月贷款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170006')
      }}</el-descriptions-item>
    <el-descriptions-item label="近1个月贷款总金额">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170007')
      }}</el-descriptions-item>
    <el-descriptions-item label="近3个月贷款总金额">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170008')
      }}</el-descriptions-item>
    <el-descriptions-item label="近6个月贷款总金额">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170009')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月贷款总金额">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170010')
      }}</el-descriptions-item>
    <el-descriptions-item span="2" label="近24个月贷款总金额">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170011')
      }}</el-descriptions-item>
    <el-descriptions-item label="近1个月贷款机构数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170016')
      }}</el-descriptions-item>
    <el-descriptions-item label="近3个月贷款机构数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170017')
      }}</el-descriptions-item>
    <el-descriptions-item label="近6个月贷款机构数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170018')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月贷款机构数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170019')
      }}</el-descriptions-item>
    <el-descriptions-item span="2" label="近24个月贷款机构数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170020')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月贷款金额在1k及以下的笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170012')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月贷款金额在1k-3k的笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170013')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月贷款金额在3k-10k的笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170014')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月贷款金额在1w以上的笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170015')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月消金类贷款机构数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170021')
      }}</el-descriptions-item>
    <el-descriptions-item label="近24个月消金类贷款机构数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170022')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月网贷类贷款机构数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170023')
      }}</el-descriptions-item>
    <el-descriptions-item span="2" label="近24个月网贷类贷款机构数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170024')
      }}</el-descriptions-item>
    <el-descriptions-item label="近6个月M0+逾期贷款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170025')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月M0+逾期贷款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170026')
      }}</el-descriptions-item>
    <el-descriptions-item label="近24个月M0+逾期贷款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170027')
      }}</el-descriptions-item>
    <el-descriptions-item label="近6个月M1+逾期贷款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170028')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月M1+逾期贷款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170029')
      }}</el-descriptions-item>
    <el-descriptions-item label="近24个月M1+逾期贷款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170030')
      }}</el-descriptions-item>
    <el-descriptions-item label="近6个月累计逾期金额">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170031')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月累计逾期金额">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170032')
      }}</el-descriptions-item>
    <el-descriptions-item label="近24个月累计逾期金额">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170033')
      }}</el-descriptions-item>
    <el-descriptions-item label="近1个月失败扣款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170035')
      }}</el-descriptions-item>
    <el-descriptions-item label="近3个月失败扣款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170036')
      }}</el-descriptions-item>
    <el-descriptions-item label="近6个月失败扣款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170037')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月失败扣款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170038')
      }}</el-descriptions-item>
    <el-descriptions-item span="2" label="近24个月失败扣款笔数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170039')
      }}</el-descriptions-item>
    <el-descriptions-item label="近1个月履约贷款总金额">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170040')
      }}</el-descriptions-item>
    <el-descriptions-item label="近3个月履约贷款总金额">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170041')
      }}</el-descriptions-item>
    <el-descriptions-item label="近6个月履约贷款总金额">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170042')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月履约贷款总金额">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170043')
      }}</el-descriptions-item>
    <el-descriptions-item span="2" label="近24个月履约贷款总金额">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170044')
      }}</el-descriptions-item>
    <el-descriptions-item label="近1个月履约贷款次数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170045')
      }}</el-descriptions-item>
    <el-descriptions-item label="近3个月履约贷款次数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170046')
      }}</el-descriptions-item>
    <el-descriptions-item label="近6个月履约贷款次数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170047')
      }}</el-descriptions-item>
    <el-descriptions-item label="近12个月履约贷款次数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170048')
      }}</el-descriptions-item>
    <el-descriptions-item span="2" label="近24个月履约贷款次数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170049')
      }}</el-descriptions-item>
    <el-descriptions-item label="最近一次履约距今天数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170050')
      }}</el-descriptions-item>
    <el-descriptions-item label="贷款已结清订单数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170052')
      }}</el-descriptions-item>
    <el-descriptions-item label="信用贷款时长">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170053')
      }}</el-descriptions-item>
    <el-descriptions-item span="3" label="最近一次贷款放款时间">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'behavior_report_detail', 'B22170054')
      }}</el-descriptions-item>
  </el-descriptions>
  <el-divider>信用状态报告</el-divider>
  <el-descriptions
    border
    :column="3"
    label-width="220"
  >
    <el-descriptions-item label="网贷授信额度">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'current_report_detail', 'C22180001')
      }}</el-descriptions-item>
    <el-descriptions-item label="网贷额度置信度">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'current_report_detail', 'C22180002')
      }}</el-descriptions-item>
    <el-descriptions-item label="网络贷款类机构数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'current_report_detail', 'C22180003')
      }}</el-descriptions-item>
    <el-descriptions-item label="网络贷款类产品数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'current_report_detail', 'C22180004')
      }}</el-descriptions-item>
    <el-descriptions-item label="网络贷款机构最大授信额度">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'current_report_detail', 'C22180005')
      }}</el-descriptions-item>
    <el-descriptions-item label="网络贷款机构平均授信额度">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'current_report_detail', 'C22180006')
      }}</el-descriptions-item>
    <el-descriptions-item label="消金贷款类机构数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'current_report_detail', 'C22180007')
      }}</el-descriptions-item>
    <el-descriptions-item label="消金贷款类产品数">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'current_report_detail', 'C22180008')
      }}</el-descriptions-item>
    <el-descriptions-item label="消金贷款类机构最大授信额度">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'current_report_detail', 'C22180009')
      }}</el-descriptions-item>
    <el-descriptions-item label="消金贷款类机构平均授信额度">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'current_report_detail', 'C22180010')
      }}</el-descriptions-item>
    <el-descriptions-item label="消金建议授信额度">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'current_report_detail', 'C22180011')
      }}</el-descriptions-item>
    <el-descriptions-item label="消金额度詈信度">{{
        scoreValueToTxt(fkTabItem.raw_data, 'leida_v4', 'current_report_detail', 'C22180012')
      }}</el-descriptions-item>
  </el-descriptions>
</template>

<style scoped lang="less">
.fk-tab-title {
  margin-bottom: 10px;
  overflow-y: auto;
}
</style>