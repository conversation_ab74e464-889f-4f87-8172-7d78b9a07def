import { defHttp } from '@/utils/http';
import { BaseResponse } from '@/api/base';


enum Api {
  GetBalance = '/finance/balance',
  RechargeDetail = '/finance/rechargeDetail',
  QueryDetail = '/finance/queryDetail',
  Recharge = '/finance/recharge'
}


// 查询部门余额列表
export function getDepartmentBalance() {
  return defHttp.get<BaseResponse<any>>({
    url: Api.GetBalance
  }, {
    errorMessageMode: 'message'
  });
}
// 充值明细列表
export function getRechargeDetail(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.RechargeDetail,
    data: params,
  }, {
    errorMessageMode: 'message'
  });
}
// 查询明细列表
export function getQueryDetail(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.QueryDetail,
    data: params,
  }, {
    errorMessageMode: 'message'
  });
}
// 部门充值
export function recharge(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.Recharge,
    data: params,
  }, {
    errorMessageMode: 'message'
  });
}
