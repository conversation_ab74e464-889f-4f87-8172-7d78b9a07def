
// 基础API响应接口
import { defHttp } from '@/utils/http';

export interface BaseResponse<T = any> {
  code: number;
  message: string;
  data: T;
}
enum Api {
  // 账户信息接口
  GetMerchantsBalance = '/payment/manager/getMerchantsBalance',
  // 收入明细接口
  GetIncomeDetails = '/statistics/statisticscontroller/getIncomeDetails',
  // 支出明细接口
  GetExpenseDetails = '/statistics/statisticscontroller/getExpenseDetails',
}
// 账户信息接口-获取账户余额
export function getMerchantsBalance() {
  return defHttp.get<BaseResponse<any>>({
    url: Api.GetMerchantsBalance
  }, {
    errorMessageMode: 'message'
  });
}
// 收入明细接口
export function getIncomeDetails(params: any) {
  return defHttp.get<BaseResponse<any>>({
    url: Api.GetIncomeDetails,
    params: params
  }, {
    errorMessageMode: 'message'
  });
}
// 支出明细接口
export function getExpenseDetails(params: any) {
  return defHttp.get<BaseResponse<any>>({
    url: Api.GetExpenseDetails,
    params: params
  }, {
    errorMessageMode: 'message'
  });
}
