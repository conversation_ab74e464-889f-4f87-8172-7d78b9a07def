package productrules

import (
	"context"
	"errors"
	"fincore/model"
	"fincore/utils/repayment"
	"fincore/utils/shopspringutils"
)

// calculationResult 检查账单计算结果
func calculationResult(rule *ProductRule) error {

	// 计算产品规则结果
	calculationResult, err := repayment.CalculateRepaymentSchedule(rule.ProductRule)
	if err != nil {
		return err
	}

	// 如果有用户自定义的计算
	// 比较待还本金之和、待还利息之和、待还担保费之和是否相等
	// 比较应还总额是否相等
	// 比较实际放款金额是否相等
	if rule.CalculationResult != nil {
		customCalculation := rule.CalculationResult
		var totalPrincipal, totalInterest, totalGuaranteeFee, customerTotalAssetManagementFee, customerTotalRepayableAmount float64
		for i, period := range calculationResult.Periods {
			totalPrincipal = shopspringutils.AddAmountsWithDecimal(totalPrincipal, period.DuePrincipal)
			totalInterest = shopspringutils.AddAmountsWithDecimal(totalInterest, period.DueInterest)
			totalGuaranteeFee = shopspringutils.AddAmountsWithDecimal(totalGuaranteeFee, period.DueGuaranteeFee)
			// 重新计算用户自定义的资管费用
			customerPeriod := customCalculation.Periods[i]
			diffPrincipal := shopspringutils.SubtractAmountsWithDecimal(customerPeriod.DuePrincipal, period.DuePrincipal)
			newPrincipal := shopspringutils.AddAmountsWithDecimal(period.DuePrincipal, diffPrincipal)

			diffInterest := shopspringutils.SubtractAmountsWithDecimal(customerPeriod.DueInterest, period.DueInterest)
			newInterest := shopspringutils.AddAmountsWithDecimal(period.DueInterest, diffInterest)

			newAssetManagementFee := shopspringutils.AddAmountsWithDecimal(shopspringutils.AddAmountsWithDecimal(newPrincipal, newInterest), customerPeriod.DueOtherFees)
			customerPeriod.AssetManagementFee = newAssetManagementFee
			customCalculation.Periods[i] = customerPeriod
			customerTotalAssetManagementFee = shopspringutils.AddAmountsWithDecimal(customerTotalAssetManagementFee, customerPeriod.AssetManagementFee)

			customerTotalRepayableAmount = shopspringutils.AddAmountsWithDecimal(customerTotalRepayableAmount, customerPeriod.TotalDueAmount)
		}

		customCalculation.TotalAssetManagementFee = customerTotalAssetManagementFee
		customCalculation.TotalRepayableAmount = customerTotalRepayableAmount

		// 待还本金
		// 待还利息
		// 待还担保费
		// 应还总额
		// 实际放款金额
		// 都需要大于等于 0
		if totalPrincipal < 0 {
			return errors.New("待还本金不能小于0")
		}
		if totalInterest < 0 {
			return errors.New("待还利息不能小于0")
		}
		if totalGuaranteeFee < 0 {
			return errors.New("待还担保费不能小于0")
		}
		if calculationResult.TotalRepayableAmount < 0 {
			return errors.New("应还总额不能小于0")
		}
		if calculationResult.DisbursementAmount < 0 {
			return errors.New("实际放款金额不能小于0")
		}

		// 比对用户自定义输入的值与计算出的值是否一致
		if shopspringutils.CompareAmountsWithDecimal(totalPrincipal, customCalculation.TotalPrincipal) != shopspringutils.Equal {
			return errors.New("待还本金与账单本金不相等")
		}
		if shopspringutils.CompareAmountsWithDecimal(totalInterest, customCalculation.TotalInterest) != shopspringutils.Equal {
			return errors.New("待还利息与账单利息不相等")
		}
		if shopspringutils.CompareAmountsWithDecimal(totalGuaranteeFee, customCalculation.TotalGuaranteeFee) != shopspringutils.Equal {
			return errors.New("待还担保费与账单担保费不相等")
		}

		if shopspringutils.CompareAmountsWithDecimal(calculationResult.TotalRepayableAmount, customCalculation.TotalRepayableAmount) != shopspringutils.Equal {
			return errors.New("应还总额与账单应还总额不相等")
		}

		if shopspringutils.CompareAmountsWithDecimal(calculationResult.DisbursementAmount, customCalculation.DisbursementAmount) != shopspringutils.Equal {
			return errors.New("实际放款金额与账单实际放款金额不相等")
		}

		if shopspringutils.CompareAmountsWithDecimal(calculationResult.TotalAssetManagementFee, customCalculation.TotalAssetManagementFee) != shopspringutils.Equal {
			return errors.New("计算的资管费用与账单资管费用不相等")
		}
	} else {
		*rule.CalculationResult = *calculationResult
	}

	return nil
}

// MatchProductRules 匹配产品规则
func MatchProductRules(ctx context.Context, products []*model.ProductRules, customerID int) ([]*model.ProductRules, error) {
	// 如果用户不是新客户，只能匹配 rule_category 为复购的产品
	// 查询用户订单,订单状态为放款中或交易完成
	orders, err := model.NewBusinessLoanOrdersService(ctx).GetOrdersByCustomerID(int(customerID), model.GetOrdersByCustomerIDCondition{
		UserID: int(customerID),
		Status: []int{model.OrderStatusDisbursed, model.OrderStatusCompleted},
	}, "id")

	if err != nil {
		return nil, err
	}

	var matchedProducts []*model.ProductRules
	if len(orders) > 0 {
		// 如果用户有订单，则只能匹配 rule_category 为复购的产品
		for _, product := range products {
			if product.RuleCategory == model.RuleCategoryRepeatPurchase {
				matchedProducts = append(matchedProducts, product)
			}
		}
		products = matchedProducts
	}

	return products, nil
}
