import { defHttp } from '@/utils/http';
import { BaseResponse } from '@/api/base';


enum Api {
  GetDetail = '/credit/detail',
  SearchQuery = '/credit/query',
  Balance = '/credit/balance',
  Record = '/credit/record',
  deleteRecord = '/credit/record',
  createQuery = '/credit/createQuery'
}


// 查询用户列表
export function getUserDetail(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.GetDetail,
    data: params
  }, {
    errorMessageMode: 'message'
  });
}
// 用户信用查询
export function searchUserQuery(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.SearchQuery,
    data: params
  }, {
    errorMessageMode: 'message'
  });
}
// 部门余额
export function getDepartmentBalance() {
  return defHttp.get<BaseResponse<any>>({
    url: Api.Balance
  }, {
    errorMessageMode: 'message'
  });
}
// 记录详情
export function getRecordDetail(params: any) {
  return defHttp.get<BaseResponse<any>>({
    url: Api.Record,
    params: params
  }, {
    errorMessageMode: 'message'
  });
}
// 删除记录
export function deleteRecord(params: any) {
  return defHttp.delete<BaseResponse<any>>({
    url: Api.deleteRecord+'?id='+params.id,
    params: params
  }, {
    errorMessageMode: 'message'
  });
}
// 创建查询
export function createQuery(params: any) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.createQuery,
    data: params
  }, {
    errorMessageMode: 'message'
  });
}
