div[dnd-trigger-key="table"], div[toolbar-trigger-key="table"] {
    margin-left: -13px;
    margin-top: -13px;
}

.data-table {
    border: none;
    position: relative;
    z-index: 1;
    table-layout: fixed;
    white-space: pre-wrap;
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
}

div[data-card-key="table"].card-selected .data-table, div[data-card-key="table"].card-selected-other .data-table {
  background: transparent
}

.am-engine [data-card-key="table"].card-selected [data-card-element="center"].data-card-background-selected {
    background: transparent;
}

.am-engine [data-card-key="table"].card-selected [data-card-element="center"].data-card-background-selected .table-wrapper {
    background: rgba(27, 162, 227, 0.2);
}

.am-engine-mobile div[data-card-key="table"].card-activated {
  margin-left: 20px;
}

.data-table tr,.data-table td {
    position: relative;
}

.data-table tr {
    height: 35px;
}

.data-table tr td {
    border: none;
    vertical-align: top;
    cursor: text;
}

.data-table tr td[table-cell-selection]:after {
    content: ' ';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(180, 213, 254, 0.5);
    pointer-events: none;
    z-index: 2;
}

.drag-selecting .data-table, .data-table tr td[table-cell-selection] {
    caret-color:transparent
}

.data-table tr td[table-cell-selection] ::selection{
    background: transparent !important;
}

.table-wrapper {
    position: relative;
    cursor: default;
}

.table-wrapper.active {
    margin-top: -42px;
    width: 100%;
}

.table-wrapper.scrollbar-show {
    margin-bottom: -10px;
}

.table-wrapper.data-table-highlight tr td[table-cell-selection]:after {
    background: transparent;
}

.table-wrapper .table-header {
    position: absolute;
    visibility: hidden;
    left: -13px;
    top: 28px;
    width: 14px;
    height: 14px;
    cursor: pointer;
    z-index: 4;
    background-color: #ffffff;
}

.table-wrapper .table-header .table-header-item {
    border: 1px solid #dfdfdf;
    background-color: #f2f3f5;
    border-top-left-radius: 50%;
    width: 100%;
    height: 100%;
}

.table-wrapper .table-header:hover {
    background-color: #ffffff;
}

.table-wrapper.data-table-highlight-all .table-header .table-header-item {
    background: rgba(255, 77, 79, 0.4) !important;
}

.table-wrapper .table-header-item:hover{
    background-color: #e2e4e6;
}

.table-wrapper.active .table-header {
    visibility:visible
}

.table-wrapper .table-header.selected .table-header-item {
    background: #4daaff;
    border-color: #4daaff;
}

.table-wrapper .table-cols-header {
    position: relative;
    height: 13px;
    display: none;
    width: 100%;
    cursor: default;
}

.table-wrapper.active .table-cols-header {
    display: flex;
}

.table-wrapper .table-cols-header .table-cols-header-item {
    position: relative;
    height: 13px;
    width: auto;
    border: 1px solid #dfdfdf;
    border-bottom: 0 none;
    overflow: visible;
    background: #f2f3f5;
    cursor: pointer;
    border-right: 0 none;
}

.table-wrapper .table-cols-header .table-cols-header-item.active{
    background-color: #e2e4e6;
}

.table-wrapper .table-cols-header .table-cols-header-item:first-child {
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
}
.table-wrapper .table-cols-header .table-cols-header-item:last-child {
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
    border-right: 1px solid #dfdfdf;
}
.table-wrapper.data-table-highlight-col .table-cols-header .table-cols-header-item.active, .table-wrapper.data-table-highlight-all .table-cols-header .table-cols-header-item {
    background: rgba(255, 77, 79, 0.4) !important;
}
.table-wrapper .table-cols-header .table-cols-header-item.selected {
    background: #fff;
    z-index: 1;
    border-radius: 0;
    border-bottom: 0;
    cursor: move;
}
.table-wrapper .table-cols-header .table-cols-header-item.selected .col-dragger {
    display: flex;
    position: absolute;
    left: -1px;
    top: -1px;
    right: -1px;
    bottom: -1px;
    background: #4daaff;
    border-radius: 0;
    z-index: 1;
    justify-content: center;
    align-items: center;
}

.table-wrapper.data-table-highlight-col .table-cols-header .table-cols-header-item .col-dragger,.table-wrapper.data-table-highlight-all .table-cols-header .table-cols-header-item .col-dragger {
    background: transparent !important;
}
.table-wrapper .table-cols-header .table-cols-header-item.selected .col-dragger .drag-info {
    display: none;
}
.table-wrapper .table-cols-header .table-cols-header-item.no-dragger .col-dragger .data-icon {
    display: none;
}
.table-wrapper .table-cols-header .table-cols-header-item.dragging .col-dragger {
    height: 50px;
}
.table-wrapper .table-cols-header .table-cols-header-item.dragging .col-dragger .drag-info {
    display: block;
    color: #fff;
}
.table-wrapper .table-cols-header .table-cols-header-item .cols-trigger {
    position: absolute;
    right: -1px;
    top: -1px;
    width: 2px;
    height: 14px;
    z-index: 10;
}

.am-engine-mobile .table-wrapper .table-cols-header .table-cols-header-item .cols-trigger {
  right: -3px;
  width: 6px;
}

.am-engine:not(.am-engine-mobile) .table-wrapper .table-cols-header .table-cols-header-item .cols-trigger.active {
    background: #0589f3;
    cursor: col-resize;
}

.am-engine:not(.am-engine-mobile) .table-wrapper .table-cols-header .table-cols-header-item .cols-trigger.dragging {
    width: 2px;
    background: #0589f3;
    right: -1px;
}

.table-wrapper .table-cols-header .table-cols-header-item .col-dragger {
    text-align: center;
    display: none;
}
.table-wrapper .table-cols-header .table-cols-header-item .col-dragger .data-icon-drag {
    font-size: 10px;
    color: #fff;
}

.table-wrapper .table-cols-header .table-cols-header-item .col-dragger .data-icon-drag::before {
    transform: rotate(90deg);
    display: inline-block;
}

.table-wrapper.data-table-highlight-col .table-cols-header .table-cols-header-item .col-dragger .data-icon-drag,.table-wrapper.data-table-highlight-all .table-cols-header .table-cols-header-item .col-dragger .data-icon-drag{
    display: none;
}
.table-wrapper .table-cols-header.dragging .table-cols-header-item .cols-trigger,.table-wrapper.drag-selecting .table-cols-header-item .cols-trigger{
    display: none;
}
.table-wrapper .table-cols-header.resize .table-cols-header-item {
    cursor: col-resize;
}
.table-wrapper .table-rows-header {
    position: absolute;
    left: -13px;
    top: 41px;
    width: 14px;
    z-index: 128;
    border-right: 0;
    display: none;
}

.table-wrapper.active .table-rows-header {
    display: block;
}

.table-wrapper .table-rows-header .table-rows-header-item {
    position: relative;
    width: 100%;
    border: 1px solid #dfdfdf;
    border-bottom: 0;
    background: #f2f3f5;
    cursor: pointer;
}

.table-wrapper .table-rows-header .table-rows-header-item.active
{
    background-color: #e2e4e6;
}

/* .table-wrapper .table-rows-header .table-rows-header-item:nth-child(3) {
    border-top: 0 none;
} */

.table-wrapper .table-rows-header .table-rows-header-item:last-child{
    border-bottom: 1px solid #dfdfdf;
}

.table-wrapper.data-table-highlight-row .table-rows-header .table-rows-header-item.active,.table-wrapper.data-table-highlight-all .table-rows-header .table-rows-header-item {
    background: rgba(255, 77, 79, 0.4) !important;
}

.table-wrapper .table-rows-header .table-rows-header-item.selected {
    width: 14px;
    background: #fff;
    cursor: move;
}
.table-wrapper .table-rows-header .table-rows-header-item.selected .row-dragger {
    display: flex;
    position: absolute;
    align-items: center;
    text-align: left;
    white-space: nowrap;
    content: ' ';
    left: -1px;
    top: -1px;
    bottom: -1px;
    right: -1px;
    background: #4daaff;
    border-radius: 0;
    z-index: 1;
}
.table-wrapper.data-table-highlight-row .table-rows-header .table-rows-header-item .row-dragger,.table-wrapper.data-table-highlight-all .table-rows-header .table-rows-header-item .row-dragger{
    background: transparent !important;
}
.table-wrapper .table-rows-header .table-rows-header-item.selected .row-dragger .drag-info {
    display: none;
}
.table-wrapper .table-rows-header .table-rows-header-item.no-dragger .row-dragger .data-icon {
    display: none;
}
.table-wrapper .table-rows-header .table-rows-header-item.dragging .row-dragger {
    width: 150px;
}
.table-wrapper .table-rows-header .table-rows-header-item.dragging .row-dragger .drag-info {
    margin-left: 5px;
    display: flex;
    padding: 10px;
    color: #fff;
}
.table-wrapper .table-rows-header .table-rows-header-item .rows-trigger {
    position: absolute;
    bottom: -1px;
    height: 2px;
    width: 14px;
    left: -1px;
    z-index: 10;
    cursor: row-resize;
}

.am-engine-mobile .table-wrapper .table-rows-header .table-rows-header-item .rows-trigger {
  bottom: -3px;
  height: 6px;
}

.am-engine:not(.am-engine-mobile) .table-wrapper .table-rows-header .table-rows-header-item .rows-trigger.active {
    background: #0589f3;
}

.am-engine:not(.am-engine-mobile) .table-wrapper .table-rows-header .table-rows-header-item .rows-trigger.dragging {
    height: 2px;
    background: #0589f3;
    bottom: -1px;
}

.table-wrapper .table-rows-header .table-rows-header-item .row-dragger {
    display: none;
}
.table-wrapper .table-rows-header .table-rows-header-item .row-dragger .data-icon-drag {
    font-size: 10px;
    color: #fff;
    margin-left: 1px;
}

.table-wrapper.data-table-highlight-row .table-rows-header .table-rows-header-item .row-dragger .data-icon-drag,.table-wrapper.data-table-highlight-all .table-rows-header .table-rows-header-item .row-dragger .data-icon-drag{
    display: none;
}

.table-wrapper .table-rows-header .table-rows-header-item .row-dragger .drag-info {
    height: 100%;
    display: flex;
    align-items: center;
}
.table-wrapper .table-rows-header.dragging .table-rows-header-item .rows-trigger,.table-wrapper.drag-selecting .table-rows-header-item .rows-trigger {
    display: none;
}
.table-wrapper .table-rows-header.resize .table-rows-header-item {
    cursor: row-resize;
}

.table-wrapper .table-viewport {
    position: relative;
    overflow: hidden;
    overflow-y: hidden;
    cursor: text;
}

.table-wrapper.active .table-viewport{
    padding-top: 28px;
    padding-left: 13px;
    margin-left: -13px;
}

.table-wrapper .table-viewport .scrollbar-shadow-left {
    top: 0;
    bottom: 10px;
}

.table-wrapper.active .table-viewport .scrollbar-shadow-left {
    top: 28px;
    margin-left: 14px;
}

.table-wrapper .table-viewport .scrollbar-shadow-right {
    top: 0;
    bottom: 10px;
}

.table-wrapper.active .table-viewport .scrollbar-shadow-right {
    top: 28px;
}

.table-wrapper .table-placeholder {
    position: absolute;
    border: 1px solid #008dff;
    background: #008dff;
    display: none;
    z-index: 126;
}
.table-wrapper .table-menubar {
    position: absolute;
    top: -99999px;
    left: -99999px;
    padding: 4px 0;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.08);
    background: #fff;
    /* \u4ee3\u7801\u5757\u7684 codemirror \u9ed8\u8ba4\u7ed9\u6bcf\u884c\u4ee3\u7801\u7684 pre \u8bbe\u7f6e\u4e86 z-index 2, \u5982\u679c\u8868\u683c\u5728\u4ee3\u7801\u5757\u524d\u9762\uff0c\u9020\u6210\u53f3\u952e\u83dc\u5355\u65e0\u6cd5\u70b9\u51fb */
    z-index: 130;
    min-width: 240px;
}
.table-wrapper .table-menubar .table-menubar-item {
    padding: 6px 16px;
    cursor: default;
}
.table-wrapper .table-menubar .table-menubar-item:hover {
    background: #f0f0f0;
}

.table-wrapper .table-menubar .table-menubar-item.disabled {
    color: #aaa;
    display: none;
}

.table-wrapper .table-menubar .table-menubar-item .table-menubar-item-input {
    width: 46px;
    line-height: 12px;
    font-size: 12px;
    outline: none;
    border: 1px solid #dadada;
    border-radius: 4px;
    text-align: center;
}

.table-wrapper .table-menubar .table-menubar-item .table-menubar-item-input::selection {
    color: inherit;
    background:transparent
}

.table-wrapper .table-menubar .table-menubar-item .table-menubar-item-input:focus::selection
{
    color: #fff;
    background: #1890ff;
}

.table-wrapper .table-menubar .split {
    height: 0;
    border-top: 1px solid #e8e8e8;
    margin: 2px 0;
}

.table-wrapper .table-main-content {
    margin: 2px 2px;
    padding: 4px 4px;
    position: relative;
    z-index: 3;
}

.table-wrapper .table-main-content [data-card-key] {
    max-width: 100%;
}

.table-wrapper .table-main-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
    bottom: 0;
    pointer-events: none;
}

.table-wrapper .table-main-bg .table-main-border-top {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
    height: 0;
    border-top: 1px solid rgb(217, 217, 217);
}

.table-wrapper:not(.active) [data-table-no-border="true"] .table-main-bg .table-main-border-top {
    border-top-color: transparent;
}

.table-wrapper.active [data-table-no-border="true"] .table-main-bg .table-main-border-top {
    border-top-style: dashed;
}

.table-wrapper .table-main-bg .table-main-border-right {
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    right: -1px;
    z-index: 1;
    width: 0;
    border-right: 1px solid rgb(217, 217, 217);
}

.table-wrapper:not(.active) [data-table-no-border="true"] .table-main-bg .table-main-border-right {
    border-right-color: transparent;
}

.table-wrapper.active [data-table-no-border="true"] .table-main-bg .table-main-border-right {
    border-right-style: dashed;
}

.table-wrapper tr td.table-last-row .table-main-bg .table-main-border-right {
    display: block;
    right: 0;
}

.table-wrapper .table-main-bg .table-main-border-bottom {
    display: none;
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    z-index: 1;
    height: 0;
    border-bottom: 1px solid rgb(217, 217, 217);
}

.table-wrapper:not(.active) [data-table-no-border="true"] .table-main-bg .table-main-border-bottom {
    border-bottom-color: transparent;
}

.table-wrapper.active [data-table-no-border="true"] .table-main-bg .table-main-border-bottom {
    border-bottom-style: dashed;
}

.table-wrapper tr td.table-last-column .table-main-bg .table-main-border-bottom{
    display: block;
    bottom: 0;
}

.table-wrapper .table-main-bg .table-main-border-left {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    width: 0;
    border-left: 1px solid rgb(217, 217, 217);
}

.table-wrapper:not(.active) [data-table-no-border="true"] .table-main-bg .table-main-border-left {
    border-left-color: transparent;
}

.table-wrapper.active [data-table-no-border="true"] .table-main-bg .table-main-border-left {
    border-left-style: dashed;
}

.table-wrapper .table-highlight {
    background: #ff4d4f;
    opacity: 0.08;
    position: absolute;
    z-index: 2;
    pointer-events: none;
    display: none;
}

.table-wrapper.scrollbar-show .data-scrollable.scroll-x {
    padding-bottom: 10px;
}

.table-wrapper .data-scrollable.scroll-x {
    padding-bottom: 0;
}

.table-wrapper .data-scrollable.scroll-x:hover {
    overflow: hidden;
}

.table-wrapper.scrollbar-show .data-scrollable.scroll-x .data-scrollbar-x{
    margin-bottom: 2px;
}

.table-wrapper .data-scrollable .data-scrollbar.data-scrollbar-x {
    height: 6px;
    z-index: 5;
}

.table-wrapper .data-scrollable .data-scrollbar.data-scrollbar-x .data-scrollbar-trigger {
    height: 6px;
}

.table-wrapper .table-rows-header .table-row-delete-button,.table-wrapper .table-rows-header .table-row-add-button {
    position: absolute;
    right: 100%;
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2;
    width: 24px;
    height: 24px;
    border: 1px solid #e4e4e4;
    border-radius: 2px;
    cursor: pointer;
    background-color: #fff;
    background-position: center center;
    background-repeat: no-repeat;

    left: 14px;
    margin-left: 2px;
    margin-top: -2px;
}

.table-wrapper .table-rows-header .table-row-delete-button .data-icon,.table-wrapper .table-rows-header .table-row-add-button .data-icon {
    font-size: 12px;
}

.table-wrapper .table-rows-header .table-row-add-button {
    left: -30px;
    margin-top: -12px;
}

.table-wrapper .table-rows-header .table-row-delete-button:hover,.table-wrapper .table-col-delete-button:hover {
    color:#ff4d4f;
}

.table-wrapper .table-col-delete-button, .table-wrapper .table-col-add-button {
    position: absolute;
    bottom: 100%;
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 128;
    margin-bottom: 4px;
    width: 24px;
    height: 24px;
    border: 1px solid #e4e4e4;
    border-radius: 2px;
    cursor: pointer;
    background: #fff;
    background-position: center center;
    background-repeat: no-repeat;
    margin-left: -12px;
}

.table-wrapper .table-col-delete-button .data-icon, .table-wrapper .table-col-add-button .data-icon {
    font-size: 12px;
}

.table-wrapper .table-cols-header .table-col-add-button .table-col-add-split-button {
    position: absolute;
    width: 2px;
    left: 10px;
    top: 22px;
    background: #008dff;
    display: none;
}

.table-wrapper .table-rows-header .table-row-add-button .table-row-add-split-button {
    position: absolute;
    height: 2px;
    left: 22px;
    background: #008dff;
    display: none;
}

.table-wrapper .table-col-add-button:hover, .table-wrapper .table-row-add-button:hover {
    color: #008dff;
}

.table-wrapper .table-col-add-button:hover .table-col-add-split-button{
    display: block;
}

.table-wrapper .table-row-add-button:hover .table-row-add-split-button{
    display: block;
}

.data-table-reader .data-table tr td {
    border: 1px solid rgb(217, 217, 217);
    cursor: auto;
    padding: 4px 8px;
}

.data-table-reader .data-table[data-table-no-border="true"] tr td {
    border:0 none
}

.data-table-reader.data-scrollable.scroll-x {
    padding-bottom: 10px;
}

.data-table-reader .scrollbar-shadow-left, .data-table-reader .scrollbar-shadow-right {
    bottom: 10px;
}

.data-table-reader.scrollbar-show.data-scrollable.scroll-x .data-scrollbar-x{
    margin-bottom: 2px;
}

.data-table-reader.data-scrollable .data-scrollbar.data-scrollbar-x {
    height: 6px;
}

.data-table-reader.data-scrollable .data-scrollbar.data-scrollbar-x .data-scrollbar-trigger {
    height: 6px;
}

[data-card-key="table"].data-card-block-max > [data-card-element="body"] > [data-card-element="center"] {
    padding: 48px;
    margin-top: 4px;
}
/**
表格可溢出样式
**/
.table-wrapper.table-overflow {
    width: auto;
}
