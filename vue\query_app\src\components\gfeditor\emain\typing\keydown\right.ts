import isHotkey from 'is-hotkey';
import { TypingHandleInterface } from '../../types';
import Default<PERSON><PERSON>down from './default';

class Right extends DefaultK<PERSON>down implements TypingHandleInterface {
	type: 'keydown' | 'keyup' = 'keydown';
	hotkey = (event: KeyboardEvent) =>
		isHotkey('right', event) ||
		isHotkey('shift+right', event) ||
		isHotkey('ctrl+e', event) ||
		isHotkey('ctrl+f', event);
}
export default Right;
