import { TypingHandleInterface } from '../../types';
import Default<PERSON><PERSON>down from './default';

class Tab extends Default<PERSON><PERSON>down implements TypingHandleInterface {
	type: 'keydown' | 'keyup' = 'keydown';
	hotkey: string | string[] | ((event: KeyboardEvent) => boolean) = 'tab';

	trigger(event: KeyboardEvent): void {
		const { node } = this.engine;
		event.preventDefault();
		node.insertText('    ');
	}
}
export default Tab;
