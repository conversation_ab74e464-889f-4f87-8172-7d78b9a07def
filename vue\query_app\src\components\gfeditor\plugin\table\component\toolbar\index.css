.table-color-dropdown-trigger {
    display: flex;
}

.table-color-dropdown-trigger:hover {
    background: transparent !important;
}

.table-color-dropdown-trigger .table-color-dropdown-button-text, .table-color-dropdown-trigger .table-color-dropdown-arrow {
    display: inline-block;
    width: auto;
    margin: 0;
    text-align: center;
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 3px 3px;
    font-size: 16px;
    cursor: pointer;
    color: #595959;
    outline: none;
    margin-right: 0;
    min-width: 26px;
    border-radius: 3px 0 0 3px;
    padding: 3px 4px;
}

.table-color-dropdown-trigger:hover .table-color-dropdown-button-text, .table-color-dropdown-trigger:hover .table-color-dropdown-arrow
{
    border: 1px solid #e8e8e8;
}

.table-color-dropdown-trigger .table-color-dropdown-button-text:hover, .table-color-dropdown-trigger .table-color-dropdown-arrow:hover {
    background-color: #f5f5f5;
}

.table-color-dropdown-trigger .table-color-dropdown-arrow {
    margin-left: -1px;
    min-width: 17px;
    text-align: center;
    border-radius: 0 3px 3px 0;
}

.table-color-dropdown-trigger .table-color-dropdown-arrow .table-color-dropdown-empty {
    display: inline-block;
}

.table-color-dropdown-trigger .table-color-dropdown-arrow .data-icon-arrow {
    position: absolute;
    right: 5px;
    /* top: 12px; */
    width: 8px;
    height: 8px;
    background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4NCjxzdmcgd2lkdGg9IjhweCIgaGVpZ2h0PSI1cHgiIHZpZXdCb3g9IjAgMCA4IDUiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+DQogICAgPCEtLSBHZW5lcmF0b3I6IFNrZXRjaCA1Mi41ICg2NzQ2OSkgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+DQogICAgPHRpdGxlPkdyb3VwIENvcHkgNjwvdGl0bGU+DQogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+DQogICAgPGcgaWQ9IlN5bWJvbHMiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIG9wYWNpdHk9IjAuNDUiPg0KICAgICAgICA8ZyBpZD0idG9vbGJhciIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTMyOC4wMDAwMDAsIC0xOC4wMDAwMDApIj4NCiAgICAgICAgICAgIDxnIGlkPSJwYXJhZ3JhcGgtc3R5bGUiPg0KICAgICAgICAgICAgICAgIDxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDIyNi4wMDAwMDAsIDQuMDAwMDAwKSI+DQogICAgICAgICAgICAgICAgICAgIDxnIGlkPSJHcm91cC1Db3B5LTYiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDEwMi4wMDAwMDAsIDEyLjAwMDAwMCkiPg0KICAgICAgICAgICAgICAgICAgICAgICAgPHJlY3QgaWQ9IlJlY3RhbmdsZSIgeD0iMCIgeT0iMCIgd2lkdGg9IjgiIGhlaWdodD0iOCI+PC9yZWN0Pg0KICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD0iTTAuNTk2MDkzNzUsMi41NTcwMzEyNSBMMy43NDUzMTI1LDYuMzc4MTI1IEMzLjg3NzM0Mzc1LDYuNTI1NzgxMjUgNC4xMDg1OTM3NSw2LjUyNTc4MTI1IDQuMjQwNjI1LDYuMzc4MTI1IEw3LjQwNTQ2ODc1LDIuNTU3MDMxMjUgQzcuNTk2MDkzNzUsMi4zNDI5Njg3NSA3LjQ0NDUzMTI1LDIuMDAzOTA2MjUgNy4xNTc4MTI1LDIuMDAzOTA2MjUgTDAuODQ0NTMxMjUsMi4wMDM5MDYyNSBDMC41NTcwMzEyNSwyLjAwMzkwNjI1IDAuNDA0Njg3NSwyLjM0Mjk2ODc1IDAuNTk2MDkzNzUsMi41NTcwMzEyNSBaIiBpZD0iU2hhcGUiIGZpbGw9IiMwMDAwMDAiIGZpbGwtcnVsZT0ibm9uemVybyI+PC9wYXRoPg0KICAgICAgICAgICAgICAgICAgICA8L2c+DQogICAgICAgICAgICAgICAgPC9nPg0KICAgICAgICAgICAgPC9nPg0KICAgICAgICA8L2c+DQogICAgPC9nPg0KPC9zdmc+);
    background-repeat: no-repeat;
    transition: all 0.25s cubic-bezier(0.3, 1.2, 0.2, 1);
}

.data-table-color-tool {
    outline: none;
    width: auto;
    border-radius: 3px 3px;
    position: absolute;
    border: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    z-index: 130;
    text-indent: 0;
    top:0;
    padding: 8px 0;
    background: #fff;
}

.data-table-color-tool-mobile {
    width: calc(100vw - 20px);
}

.data-table-color-tool .data-table-color-tool-panle {
    position: relative;
    text-align: left;
    text-indent: 0;
    width: 100%;
    height: auto;
    margin-top: 8px;
    padding: 0 8px;
}

.data-table-color-tool .data-table-color-tool-group
{
    display: flex;
}

.data-table-color-tool .data-table-color-tool-group > span {
    width: 24px;
    height: 24px;
    display: inline-block;
    cursor: pointer;
    background-color: rgb(255, 255, 255);
    padding: 2px;
    border-radius: 3px;
    border-width: 1px;
    border-style: solid;
    border-color: transparent;
    border-image: initial;
    flex: 0 0 auto;
    position: relative;
}

.data-table-color-tool span.data-table-color-tool-border > span {
    border: 1px solid #e8e8e8 !important;
}

.data-table-color-tool .data-table-color-tool-group > span > span
{
    position: relative;
    width: 18px;
    height: 18px;
    display: block;
    border-radius: 2px;
    border-width: 1px;
    border-style: solid;
    border-color: transparent;
    border-image: initial;
}

.data-table-color-tool .data-table-color-tool-group > span > span > svg {
    position: absolute;
    top: -1px;
    left: 1px;
    width: 12px;
    height: 12px;
}

.data-table-color-tool .data-table-color-tool-default {
    display: flex;
    align-items: center;
    margin: 2px 0 8px;
    border-radius: 2px;
    cursor: pointer;
    padding: 2px 8px;
}   

.data-table-color-tool .data-table-color-tool-default:hover {
    background: #f5f5f5;
}

.data-table-color-tool .data-table-color-tool-default > span:first-child::after {
    content: "";
    display: block;
    position: absolute;
    top: 10px;
    left: 0px;
    width: 22px;
    height: 0;
    border-bottom: 2px solid #ff5151;
    transform: rotate(45deg);
}

.data-table-color-tool .data-table-color-tool-default .data-table-color-tool-default-text {
    width: auto;
    margin-left: 8px;
    height: auto;
    background: transparent;
}