.data-scrollable::-webkit-scrollbar {
    display: none;
    overflow: hidden;
  }
  .data-scrollable.scroll-x {
    padding-bottom: 10px;
    overflow-x: hidden;
  }
  
  .data-scrollable.scroll-y {
    padding-right: 10px;
    overflow-y: hidden;
  }

  .data-scrollable:hover .data-scrollbar {
    display: block;
  }
  .data-scrollable.scrolling {
    -webkit-user-select: none;
       -moz-user-select: none;
        -ms-user-select: none;
            user-select: none;
  }
  .data-scrollable.scrolling .data-scrollbar {
    display: block;
  }
  .data-scrollable .data-scrollbar {
    display: none;
    position: absolute;
    cursor: default;
    transition: opacity 0.3s ease-in-out;
  }
  .data-scrollable .data-scrollbar .data-scrollbar-trigger {
    position: absolute;
    background: #c1c1c1;
    border-radius: 10px;
    cursor: pointer;
  }
  .data-scrollable .data-scrollbar .data-scrollbar-trigger:hover {
    background: #888;
  }
  .data-scrollable .data-scrollbar.data-scrollbar-x {
    height: 8px;
    bottom: 0px;
  }
  .data-scrollable .data-scrollbar.data-scrollbar-x .data-scrollbar-trigger {
    height: 8px;
    min-width: 60px;
  }
  .data-scrollable .data-scrollbar.data-scrollbar-y {
    top: 0;
    width: 8px;
    height: 100%;
    right: 0px;
  }
  .data-scrollable .data-scrollbar.data-scrollbar-y .data-scrollbar-trigger {
    width: 8px;
    min-height: 60px;
  }
  .data-scrollable .scrollbar-shadow-left {
    position: absolute;
    z-index: 10;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    opacity: 0.8;
    background: linear-gradient(270deg, rgba(99, 114, 130, 0) 0, rgba(99, 114, 130, 0.16));
    background: -webkit-linear-gradient(right, rgba(99, 114, 130, 0), rgba(99, 114, 130, 0.16));
    pointer-events: none;
  }
  .data-scrollable .scrollbar-shadow-right {
    position: absolute;
    z-index: 10;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    opacity: 0.8;
    background: linear-gradient(90deg, rgba(99, 114, 130, 0) 0, rgba(99, 114, 130, 0.16));
    background: -webkit-linear-gradient(left, rgba(99, 114, 130, 0), rgba(99, 114, 130, 0.16));
    pointer-events: none;
  }