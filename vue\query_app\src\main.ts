import { createApp } from 'vue';
import ArcoVue from '@arco-design/web-vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import globalComponents from '@/components';
import router from './router';
import store from './store';
import i18n from './locale';
import directive from './directive';
import App from './App.vue';
import '@arco-design/web-vue/dist/arco.less';
import 'element-plus/dist/index.css'
// import '@arco-design/web-vue/dist/arco.css';
import '@/assets/style/global.less';
const app = createApp(App);
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

app.use(ArcoVue, {});
app.use(ArcoVueIcon);
app.use(ElementPlus, {
  locale: zhCn,
})

app.use(router);
app.use(store);
app.use(i18n);
app.use(globalComponents);
app.use(directive);

app.mount('#app');
