  .data-icon {
    font-family: "data-icon" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .data-icon-block-image:before {
    content: "\ea20";
  }

  .data-icon-inline-image:before {
    content: "\e601";
  }

  .data-icon-no-border:before {
    content: "\e608";
  }

  .data-icon-line-height:before {
    content: "\e697";
  }

  .data-icon-text:before {
    content: "\e600";
  }

  .data-icon-comment:before {
    content: "\e73e";
  }

  .data-icon-huanyuan:before {
    content: "\e60a";
  }

  .data-icon-label:before {
    content: "\e714";
  }

  .data-icon-video:before {
    content: "\e741";
  }

  .data-icon-table:before {
    content: "\e6a8";
  }

  .data-icon-h:before {
    content: "\e7a0";
  }

  .data-icon-collapse-subtree:before {
    content: "\e754";
  }

  .data-icon-expand-subtree:before {
    content: "\e792";
  }

  .data-icon-sub-node:before {
    content: "\e78e";
  }

  .data-icon-sister-node:before {
    content: "\e784";
  }

  .data-icon-sup:before {
    content: "\e790";
  }

  .data-icon-sub:before {
    content: "\e75a";
  }

  .data-icon-maximize:before {
    content: "\e752";
  }

  .data-icon-codeblock:before {
    content: "\e709";
  }

  .data-icon-emoji:before {
    content: "\e73a";
  }

  .data-icon-h4:before {
    content: "\e759";
  }

  .data-icon-h1:before {
    content: "\e75b";
  }

  .data-icon-h5:before {
    content: "\e75c";
  }

  .data-icon-h2:before {
    content: "\e75d";
  }

  .data-icon-h3:before {
    content: "\e760";
  }

  .data-icon-h6:before {
    content: "\e761";
  }

  .data-icon-liuchengtu:before {
    content: "\e61c";
  }

  .data-icon-website:before {
    content: "\e694";
  }

  .data-icon-preferences:before {
    content: "\e788";
  }

  .data-icon-hr:before {
    content: "\e76a";
  }

  .data-icon-task-list:before {
    content: "\e79f";
  }

  .data-icon-unordered-list:before {
    content: "\e777";
  }

  .data-icon-ordered-list:before {
    content: "\e795";
  }

  .data-icon-arrow-left:before {
    content: "\e748";
  }

  .data-icon-arrow-up:before {
    content: "\e769";
  }

  .data-icon-arrow-right:before {
    content: "\e779";
  }

  .data-icon-arrow-down:before {
    content: "\e79a";
  }

  .data-icon-moremark:before {
    content: "\e772";
  }

  .data-icon-clean:before {
    content: "\e74d";
  }

  .data-icon-paintformat:before {
    content: "\e756";
  }

  .data-icon-lock:before {
    content: "\e768";
  }

  .data-icon-loading:before {
    content: "\e76b";
  }

  .data-icon-unlock:before {
    content: "\e796";
  }

  .data-icon-collapse:before {
    content: "\e79e";
  }

  .data-icon-align-bottom:before {
    content: "\e72b";
  }

  .data-icon-attachment:before {
    content: "\e72c";
  }

  .data-icon-bold:before {
    content: "\e72d";
  }

  .data-icon-border-color:before {
    content: "\e72e";
  }

  .data-icon-border-all:before {
    content: "\e72f";
  }

  .data-icon-border-inner:before {
    content: "\e730";
  }

  .data-icon-border-left:before {
    content: "\e731";
  }

  .data-icon-border-bottom:before {
    content: "\e732";
  }

  .data-icon-border-none:before {
    content: "\e733";
  }

  .data-icon-box:before {
    content: "\e734";
  }

  .data-icon-border-outer:before {
    content: "\e735";
  }

  .data-icon-border-right:before {
    content: "\e736";
  }

  .data-icon-clear:before {
    content: "\e737";
  }

  .data-icon-close:before {
    content: "\e738";
  }

  .data-icon-code-example:before {
    content: "\e739";
  }

  .data-icon-clip:before {
    content: "\e73b";
  }

  .data-icon-border-up:before {
    content: "\e73c";
  }

  .data-icon-code:before {
    content: "\e73d";
  }

  .data-icon-command:before {
    content: "\e73f";
  }

  .data-icon-compact-display:before {
    content: "\e740";
  }

  .data-icon-copy:before {
    content: "\e742";
  }

  .data-icon-download:before {
    content: "\e743";
  }

  .data-icon-deletecolumn:before {
    content: "\e744";
  }

  .data-icon-cut:before {
    content: "\e745";
  }

  .data-icon-decreasedecimalplace:before {
    content: "\e746";
  }

  .data-icon-drag:before {
    content: "\e747";
  }

  .data-icon-delete:before {
    content: "\e749";
  }

  .data-icon-drag-circle:before {
    content: "\e74a";
  }

  .data-icon-deleterow:before {
    content: "\e74b";
  }

  .data-icon-edit:before {
    content: "\e74c";
  }

  .data-icon-filter:before {
    content: "\e74e";
  }

  .data-icon-expand:before {
    content: "\e74f";
  }

  .data-icon-error:before {
    content: "\e750";
  }

  .data-icon-freezerowcoloum:before {
    content: "\e751";
  }

  .data-icon-freezefirstrow:before {
    content: "\e753";
  }

  .data-icon-freezzecolumn:before {
    content: "\e755";
  }

  .data-icon-border-style:before {
    content: "\e757";
  }

  .data-icon-gotolink:before {
    content: "\e758";
  }

  .data-icon-increasedecimalplace:before {
    content: "\e75e";
  }

  .data-icon-insertrowbelow:before {
    content: "\e75f";
  }

  .data-icon-image:before {
    content: "\e762";
  }

  .data-icon-italic:before {
    content: "\e763";
  }

  .data-icon-indent:before {
    content: "\e764";
  }

  .data-icon-insertrowabove:before {
    content: "\e765";
  }

  .data-icon-insertrowright:before {
    content: "\e766";
  }

  .data-icon-left-circle-fill:before {
    content: "\e767";
  }

  .data-icon-link:before {
    content: "\e76c";
  }

  .data-icon-keyboard:before {
    content: "\e76d";
  }

  .data-icon-more:before {
    content: "\e76e";
  }

  .data-icon-merge-cells:before {
    content: "\e76f";
  }

  .data-icon-outdent:before {
    content: "\e770";
  }

  .data-icon-mention:before {
    content: "\e771";
  }

  .data-icon-plus:before {
    content: "\e773";
  }

  .data-icon-minus-circle-o:before {
    content: "\e774";
  }

  .data-icon-highlight:before {
    content: "\e775";
  }

  .data-icon-paste:before {
    content: "\e776";
  }

  .data-icon-insertrowleft:before {
    content: "\e778";
  }

  .data-icon-quote:before {
    content: "\e77a";
  }

  .data-icon-plus-circle-o:before {
    content: "\e77b";
  }

  .data-icon-right-circle-fill:before {
    content: "\e77c";
  }

  .data-icon-question-circle-o:before {
    content: "\e77d";
  }

  .data-icon-preview:before {
    content: "\e77e";
  }

  .data-icon-reload:before {
    content: "\e77f";
  }

  .data-icon-rotate-left:before {
    content: "\e780";
  }

  .data-icon-math:before {
    content: "\e781";
  }

  .data-icon-overflow:before {
    content: "\e782";
  }

  .data-icon-redo:before {
    content: "\e783";
  }

  .data-icon-searchreplace:before {
    content: "\e785";
  }

  .data-icon-save:before {
    content: "\e786";
  }

  .data-icon-singleselect:before {
    content: "\e787";
  }

  .data-icon-rotate-right:before {
    content: "\e789";
  }

  .data-icon-sort-ascending:before {
    content: "\e78a";
  }

  .data-icon-sort-descending:before {
    content: "\e78b";
  }

  .data-icon-toc:before {
    content: "\e78c";
  }

  .data-icon-solit-cells:before {
    content: "\e78d";
  }

  .data-icon-translate:before {
    content: "\e78f";
  }

  .data-icon-successful:before {
    content: "\e791";
  }

  .data-icon-strikethrough:before {
    content: "\e793";
  }

  .data-icon-undo:before {
    content: "\e794";
  }

  .data-icon-underline:before {
    content: "\e797";
  }

  .data-icon-unlink:before {
    content: "\e798";
  }

  .data-icon-wrap:before {
    content: "\e799";
  }

  .data-icon-upload:before {
    content: "\e79b";
  }

  .data-icon-zoom-out:before {
    content: "\e79c";
  }

  .data-icon-zoom-in:before {
    content: "\e79d";
  }

  .data-icon-align-center:before {
    content: "\e725";
  }

  .data-icon-align-justify:before {
    content: "\e726";
  }

  .data-icon-align-left:before {
    content: "\e727";
  }

  .data-icon-align-top:before {
    content: "\e728";
  }

  .data-icon-align-right:before {
    content: "\e729";
  }

  .data-icon-align-middle:before {
    content: "\e72a";
  }

.data-anticon {
    display: inline-block;
    font-style: normal;
    vertical-align: -0.125em;
    text-align: center;
    text-transform: none;
    line-height: 0;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
}

.data-anticon svg {
    display: inline-block;
}

@-webkit-keyframes loadingCircle {
    100% {
      transform: rotate(360deg);
    }
}
@keyframes loadingCircle {
    100% {
      transform: rotate(360deg);
    }
}

.data-anticon .data-anticon-spin {
    display: inline-block;
    -webkit-animation: loadingCircle 1s infinite linear;
    animation: loadingCircle 1s infinite linear;
}

.data-anticon > * {
    line-height: 1;
}

.am-engine {
    position: relative;
    background-color: #FFFFFF;
}

.am-engine.am-engine-placeholder:before {
  content: attr(data-placeholder);
  pointer-events: none;
  position: absolute;
  color: #bbbfc4;
  height: 0;
}

.am-engine ::selection {
    background: rgba(180, 213, 254, 0.5) !important;
    color: inherit!important;
}

.am-engine , .am-engine-view {
    font-family: 'Chinese Quote', 'Segoe UI', Roboto, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji';
	word-wrap: break-word;
	outline-style: none;
	white-space: pre-wrap;
    user-select: auto;
    font-size: 14px;
    line-height: 1.74;
    color: #262626;
    letter-spacing: .05em;
    /* pt / px 换算表：https://websemantics.uk/articles/font-size-conversion/ */
}

.am-engine > *:first-child,.am-engine-view > *:first-child {
	margin-top: 0 !important;
}

.am-engine p , .am-engine-view p {
    white-space: normal;
    margin: 0;
    line-height: 1.74;
}

.am-engine [contenteditable="true"],.am-engine-view [contenteditable="true"] {
	outline-style: none;
}

.am-engine .selection-transparent::selection,.am-engine-view .selection-transparent::selection {
	background: transparent;
}
/*---------------------------卡片 begin-----------------------*/

.am-engine [data-card-type], .am-engine-view [data-card-type] {
    white-space: normal;
}
.am-engine span[data-card-type="inline"],.am-engine-view span[data-card-type="inline"] {
	display: inline-block;
	text-indent: 0;
	vertical-align: baseline;
	white-space: initial;
}

.am-engine span[data-card-type="inline"] span[data-card-element],.am-engine-view span[data-card-type="inline"] span[data-card-element] {
	display: inline-block;
}

.am-engine span[data-card-type="inline"] span[data-card-element="center"],.am-engine-view span[data-card-type="inline"] span[data-card-element="center"] {
	vertical-align: bottom;
  border-radius: 2px;
}

.am-engine span[data-card-type="inline"] span[data-card-element="left"],
.am-engine-view span[data-card-type="inline"] span[data-card-element="left"],
.am-engine span[data-card-type="inline"] span[data-card-element="right"],.am-engine-view span[data-card-type="inline"] span[data-card-element="right"] {
	text-align: left;
	user-select: text;
  min-width: 1px;
  background: transparent;
  bottom: 0;
}

.am-engine span[data-card-type="inline"] span[data-card-element="left"],
.am-engine-view span[data-card-type="inline"] span[data-card-element="left"] {
  left: -1px;
}

.am-engine span[data-card-type="inline"] span[data-card-element="right"],.am-engine-view span[data-card-type="inline"] span[data-card-element="right"] {
  right: -1px;
}

.am-engine span[data-card-type="inline"] span[data-card-element="left"]::selection,
.am-engine-view span[data-card-type="inline"] span[data-card-element="left"]::selection,
.am-engine span[data-card-type="inline"] span[data-card-element="right"]::selection,.am-engine-view span[data-card-type="inline"] span[data-card-element="right"]::selection {
  background: transparent !important;
}

.am-engine div[data-card-type="block"],.am-engine-view div[data-card-type="block"],.am-engine span[data-card-type="inline"].data-card-block,.am-engine-view span[data-card-type="inline"].data-card-block {
	display: block;
}

.am-engine div[data-card-type="block"] > div[data-card-element="body"] > span[data-card-element="left"],.am-engine-view div[data-card-type="block"] > div[data-card-element="body"] > span[data-card-element="left"],.am-engine span[data-card-type="inline"].data-card-block > div[data-card-element="body"] > span[data-card-element="left"],.am-engine-view span[data-card-type="inline"].data-card-block > div[data-card-element="body"] > span[data-card-element="left"],.am-engine div[data-card-type="block"] > div[data-card-element="body"] > span[data-card-element="right"],.am-engine-view div[data-card-type="block"] > div[data-card-element="body"] > span[data-card-element="right"],.am-engine span[data-card-type="inline"].data-card-block > div[data-card-element="body"] > span[data-card-element="right"],.am-engine-view span[data-card-type="inline"].data-card-block > div[data-card-element="body"] > span[data-card-element="right"] {
	bottom: 0;
	position: absolute;
	width: 2px;
	overflow: hidden;
	outline: none;
	text-align: left;
	text-indent: 0;
	-webkit-box-flex: 0;
	flex: 0 0 auto;
	-webkit-user-select: text;
	user-select: text;
}

.am-engine div[data-card-type="block"] > div[data-card-element="body"] > span[data-card-element="left"],.am-engine-view div[data-card-type="block"] > div[data-card-element="body"] > span[data-card-element="left"],.am-engine span[data-card-type="inline"].data-card-block > div[data-card-element="body"] > span[data-card-element="left"],.am-engine-view span[data-card-type="inline"].data-card-block > div[data-card-element="body"] > span[data-card-element="left"] {
	left: -2px;
	text-align: left;
}

.am-engine div[data-card-type="block"] > div[data-card-element="body"] > span[data-card-element="right"],.am-engine-view div[data-card-type="block"] > div[data-card-element="body"] > span[data-card-element="right"],.am-engine span[data-card-type="inline"].data-card-block > div[data-card-element="body"] > span[data-card-element="right"],.am-engine-view span[data-card-type="inline"].data-card-block > div[data-card-element="body"] > span[data-card-element="right"] {
	right: -2px;
	text-align: right;
}

.am-engine div[data-card-type="block"] > div[data-card-element="body"] > span[data-card-element="left"]::selection,
.am-engine-view div[data-card-type="block"] > div[data-card-element="body"] > span[data-card-element="left"]::selection,
.am-engine span[data-card-type="inline"].data-card-block > div[data-card-element="body"] > span[data-card-element="left"]::selection,
.am-engine-view span[data-card-type="inline"].data-card-block > div[data-card-element="body"] > span[data-card-element="left"]::selection {
  background: transparent !important;
}

.am-engine div[data-card-type="block"] > div[data-card-element="body"] > span[data-card-element="right"]::selection,
.am-engine-view div[data-card-type="block"] > div[data-card-element="body"] > span[data-card-element="right"]::selection,
.am-engine span[data-card-type="inline"].data-card-block > div[data-card-element="body"] > span[data-card-element="right"]::selection,
.am-engine-view span[data-card-type="inline"].data-card-block > div[data-card-element="body"] > span[data-card-element="right"]::selection {
	background: transparent !important;
}

.am-engine span[data-card-element="body"],.am-engine-view span[data-card-element="body"],.am-engine div[data-card-element="body"],.am-engine-view div[data-card-element="body"] {
	position: relative;
}

.am-engine span[data-card-element="body"] [data-card-element="center"],.am-engine-view span[data-card-element="body"] [data-card-element="center"],.am-engine div[data-card-element="body"] [data-card-element="center"],.am-engine-view div[data-card-element="body"] [data-card-element="center"] {
	-webkit-user-select: text;
	user-select: text;
}
.am-engine span[data-card-element="body"] [data-element="editable"],.am-engine div[data-card-element="body"] [data-element="editable"]
{
    cursor: text;
}
/*---------------------------卡片 end-----------------------*/
