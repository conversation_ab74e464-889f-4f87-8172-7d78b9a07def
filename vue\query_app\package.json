{"name": "fincoreadmin", "description": "fincore系统", "version": "1.2.5", "private": true, "author": "fincore Team", "license": "MIT", "scripts": {"serve": "vite --config ./config/vite.config.dev.ts", "build": "vite build --config ./config/vite.config.prod.ts", "report": "cross-env REPORT=true npm run build", "preview": "npm run build && vite preview --host", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint-staged": "npx lint-staged", "prepare": "cd ../.. && husky install vue/business/.husky"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["prettier --write"], "*.vue": ["stylelint --fix", "prettier --write"], "*.{less,css}": ["stylelint --fix", "prettier --write"]}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@arco-design/web-vue": "^2.40.0", "@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^9.3.0", "axios": "^0.24.0", "blueimp-md5": "^2.18.0", "codemirror": "^5.65.3", "copy-to-clipboard": "^3.3.1", "dayjs": "^1.11.5", "diff-match-patch": "^1.0.5", "dom-align": "^1.12.2", "echarts": "^5.4.0", "element-plus": "^2.9.11", "eventemitter2": "^6.4.4", "eventemitter3": "^5.0.1", "filesize": "^6.3.0", "html2canvas": "^1.4.1", "is-hotkey": "^0.2.0", "jspdf": "^3.0.2", "keymaster": "^1.6.2", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "markdown-it": "^13.0.1", "md5": "^2.3.0", "mitt": "^3.0.0", "nprogress": "^0.2.0", "ot-json0": "^1.1.0", "photoswipe": "^4.1.3", "pinia": "^2.0.23", "qs": "^6.11.0", "query-string": "^8.0.3", "sortablejs": "^1.15.0", "tinycolor2": "^1.4.2", "unplugin-auto-import": "^19.3.0", "vue": "^3.2.40", "vue-echarts": "^6.2.3", "vue-i18n": "^9.2.2", "vue-json-pretty": "^2.0.6", "vue-router": "^4.0.14", "vue-types": "^4.1.1", "vue3-ace-editor": "^2.2.2"}, "devDependencies": {"@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@iconify/json": "^2.1.30", "@types/blueimp-md5": "^2.7.0", "@types/codemirror": "^5.60.5", "@types/diff-match-patch": "^1.0.32", "@types/is-hotkey": "^0.1.2", "@types/keymaster": "^1.6.28", "@types/lodash": "^4.14.186", "@types/lodash-es": "^4.17.6", "@types/markdown-it": "^12.2.3", "@types/nprogress": "^0.2.0", "@types/photoswipe": "^4.1.1", "@types/sharedb": "^2.2.0", "@types/sortablejs": "^1.15.0", "@types/tinycolor2": "^1.4.2", "@vitejs/plugin-vue": "^3.1.2", "@vitejs/plugin-vue-jsx": "^2.0.1", "@vue/babel-plugin-jsx": "^1.1.1", "consola": "^2.15.3", "cross-env": "^7.0.3", "husky": "^8.0.1", "less": "^4.1.3", "lint-staged": "^13.0.3", "postcss-html": "^1.5.0", "prettier": "^2.7.1", "rollup": "^3.9.1", "rollup-plugin-visualizer": "^5.8.2", "stylelint": "^14.13.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^29.0.0", "stylelint-order": "^5.0.0", "typescript": "^4.8.4", "unplugin-vue-components": "^0.22.8", "vite": "^3.2.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-style-import": "1.4.1", "vite-svg-loader": "^3.6.0", "vue-tsc": "^1.0.14"}, "engines": {"node": ">=14.0.0"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^2.56.3", "gifsicle": "5.2.0"}}